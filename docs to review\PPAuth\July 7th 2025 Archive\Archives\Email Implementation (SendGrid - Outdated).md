Implementing direct SendGrid email functionality for password management system.

## **Credential Storage Options**

### **Option 1: Azure Key Vault**

**Best for**: Production environments requiring maximum security

**Configuration**:

```json
{
  "SendGrid:ApiKey": "@Microsoft.KeyVault(SecretUri=https://[your-vault].vault.azure.net/secrets/SendGridApiKey/)",
  "SendGrid:FromEmail": "<EMAIL>",
  "SendGrid:FromName": "Your Application Name",
  "SendGrid:ResetBaseUrl": "https://[your-power-pages-domain]"
}
```

**Setup Steps**:

1. Create Azure Key Vault
2. Add SendGrid API key as secret
3. Configure Function App managed identity
4. Grant Key Vault access to Function App
5. Use Key Vault reference in app settings

### \*\*Option 2: App Settings

**Best for**: Most production scenarios - secure and easy to manage

**Configuration**:

```json
{
  "SendGrid:ApiKey": "SG.your-sendgrid-api-key-here",
  "SendGrid:FromEmail": "<EMAIL>",
  "SendGrid:FromName": "Your Application Name",
  "SendGrid:ResetBaseUrl": "https://[your-power-pages-domain]"
}
```

**Setup Steps**:

1. Go to Azure Portal → Function App → Configuration
2. Add application settings (automatically encrypted)
3. Values are secure and not visible in portal after saving

## **SendGrid Account Setup**

### \*\*Domain Authentication

**Why Required**: Without domain authentication, emails may go to spam

**Setup Steps**:

1. Go to SendGrid → Settings → Sender Authentication
2. Click "Authenticate Your Domain"
3. Enter your domain (e.g., `yourcompany.com`)
4. Add DNS records provided by SendGrid to your domain
5. Verify authentication (may take up to 48 hours)

### **Create API Key**

1. Go to SendGrid → Settings → API Keys
2. Click "Create API Key"
3. **Name**: "Power Pages Password Reset"
4. **Permissions**: "Restricted Access"
5. **Select permissions**:
   - Mail Send: Full Access
   - Stats: Read Access (optional)
6. **Copy the API key** (you won't see it again!)

### **Verify Sender Email**

1. Go to SendGrid → Settings → Sender Authentication
2. Click "Verify a Single Sender"
3. Enter your "from" email address
4. Complete verification process

### **Test Email Delivery**

1. **Call test endpoint**: `POST /api/TestEmail?email=<EMAIL>`
2. **Check your inbox** (including spam folder)
3. **Verify email content** and formatting
4. **Test reset link** (should show token in URL)

### **Test Production Flow**

1. **Go to forgot password page**
2. **Enter your email address**
3. **Check for email delivery**
4. **Click reset link**
5. **Complete password reset**
6. **Verify password changed notification**

## 📊\*\*Monitoring

### **SendGrid Dashboard**

Monitor email delivery in SendGrid dashboard:

1. Go to SendGrid → Activity
2. Check delivery statistics
3. Review bounce/spam reports
4. Monitor API usage

### **Application Insights Monitoring**

```csharp
// Email delivery metrics
_logger.LogInformation("Email delivery attempt: {Email} [Success: {Success}] [CorrelationId: {CorrelationId}]",
    email, success, correlationId);

// Track email delivery rates
_telemetryClient.TrackMetric("EmailDeliveryRate", success ? 1 : 0);
```

## **Deployment Checklist**

### **Pre-Deployment**

- [ ] SendGrid account created and verified
- [ ] Domain authentication completed
- [ ] API key created with correct permissions
- [ ] Email templates tested and approved

### **Deployment**

- [ ] Configure SendGrid settings in Azure Function
- [ ] Deploy updated PasswordService code
- [ ] Test email delivery in production environment
- [ ] Verify monitoring and logging

### **Post-Deployment**

- [ ] Monitor email delivery rates
- [ ] Check for any delivery issues
- [ ] Verify user experience with password reset flow
- [ ] Set up alerts for email delivery failures

## Microsoft Documentation References

### Azure Communication Services

- [Azure Communication Services Email overview](https://learn.microsoft.com/en-us/azure/communication-services/concepts/email/email-overview)
- [Send email with Azure Communication Services](https://learn.microsoft.com/en-us/azure/communication-services/quickstarts/email/send-email)
- [Email authentication best practices](https://learn.microsoft.com/en-us/azure/communication-services/concepts/email/email-authentication-best-practice)
- [Email delivery and reliability](https://learn.microsoft.com/en-us/azure/communication-services/concepts/email/email-delivery-reliability)

### SendGrid Integration

- [SendGrid on Azure](https://learn.microsoft.com/en-us/azure/sendgrid-dotnet-how-to-send-email)
- [SendGrid .NET SDK](https://learn.microsoft.com/en-us/azure/sendgrid-dotnet-how-to-send-email#how-to-send-an-email)
- [Email delivery best practices](https://learn.microsoft.com/en-us/azure/communication-services/concepts/email/email-delivery-reliability)

### Azure Functions Email Integration

- [Azure Functions HTTP triggers](https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-http-webhook)
- [Azure Functions configuration](https://learn.microsoft.com/en-us/azure/azure-functions/functions-app-settings)
- [Azure Functions monitoring](https://learn.microsoft.com/en-us/azure/azure-functions/functions-monitoring)
- [Azure Functions best practices](https://learn.microsoft.com/en-us/azure/azure-functions/functions-best-practices)

### Security and Key Management

- [Azure Key Vault overview](https://learn.microsoft.com/en-us/azure/key-vault/general/overview)
- [Use Key Vault references in App Service](https://learn.microsoft.com/en-us/azure/app-service/app-service-key-vault-references)
- [Managed identities for Azure resources](https://learn.microsoft.com/en-us/entra/identity/managed-identities-azure-resources/overview)
- [Azure App Service security](https://learn.microsoft.com/en-us/azure/app-service/overview-security)

### Monitoring and Diagnostics

- [Application Insights overview](https://learn.microsoft.com/en-us/azure/azure-monitor/app/app-insights-overview)
- [Application Insights for Azure Functions](https://learn.microsoft.com/en-us/azure/azure-functions/functions-monitoring)
- [Custom telemetry in Application Insights](https://learn.microsoft.com/en-us/azure/azure-monitor/app/api-custom-events-metrics)
- [Azure Monitor alerts](https://learn.microsoft.com/en-us/azure/azure-monitor/alerts/alerts-overview)

### Email Security and Compliance

- [Email security best practices](https://learn.microsoft.com/en-us/microsoft-365/security/office-365-security/email-security-in-microsoft-defender)
- [Data protection in Azure](https://learn.microsoft.com/en-us/azure/security/fundamentals/protection-customer-data)
- [Azure compliance documentation](https://learn.microsoft.com/en-us/azure/compliance/)
- [GDPR compliance in Azure](https://learn.microsoft.com/en-us/compliance/regulatory/gdpr)
