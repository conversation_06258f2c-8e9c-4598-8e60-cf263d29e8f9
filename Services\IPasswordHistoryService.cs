using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services;

public interface IPasswordHistoryService
{
    // per application passwords
    Task<Result<List<string>>> GetPasswordHistoryAsync(string applicationId, string userId, CancellationToken cancellationToken = default);
    Task<Result<bool>> UpdatePasswordHistoryAsync(string applicationId, string userId, string newPassword, CancellationToken cancellationToken = default);
    Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string applicationId, string userId, string password, CancellationToken cancellationToken = default);
}
