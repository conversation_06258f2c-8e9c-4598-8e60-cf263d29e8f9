# SendGrid Setup Guide

This guide covers what needs to be configured in SendGrid to work with the simplified email service.

## SendGrid Account Setup

### 1. Create SendGrid Account

1. Go to https://sendgrid.com and create an account
2. Verify your email address
3. Complete the account setup process

### 2. Sender Authentication (Required)

You must authenticate your sending domain or email address:

#### Option A: Domain Authentication (Recommended)

1. Go to **Settings** → **Sender Authentication**
2. Click **Authenticate Your Domain**
3. Enter your domain: `tylerlovell.com`
4. Follow the DNS setup instructions
5. Add the required CNAME records to your DNS provider
6. Verify the domain in SendGrid

#### Option B: Single Sender Verification (Quick Setup)

1. Go to **Settings** → **Sender Authentication**
2. Click **Create a Single Sender**
3. Fill in the form:
   - **From Name**: <PERSON> - Password Service
   - **From Email Address**: <EMAIL>
   - **Reply To**: <EMAIL> (or your support email)
   - **Company Address**: Your address details
4. Click **Create**
5. Check your email and verify the sender

### 3. API Key Creation

1. Go to **Settings** → **API Keys**
2. Click **Create API Key**
3. Choose **Restricted Access**
4. Give it a name: `Password Reset Service`
5. Under **Mail Send**, select **Full Access**
6. Click **Create & View**
7. **IMPORTANT**: Copy the API key immediately - you won't see it again!

### 4. Email Templates (Required)

For dynamic template functionality:

1. Go to **Email API** → **Dynamic Templates**
2. Create a new template for **password reset emails**
   - Design your template with variables like `{{resetLink}}`, `{{verificationCode}}`, and `{{applicationName}}`
   - Note the template ID for configuration
3. Create a second template for **password changed notifications**
   - Design your template with variables like `{{applicationName}}`
   - Note the template ID for configuration
4. Both template IDs are required for the application to function

## Function Configuration

The simplified email service requires only 3 essential settings. Choose one of the configuration approaches below:

### Option 1: Environment Variables (PRODUCTION - Recommended)

Set these in your Azure Function App Configuration or system environment:

```bash
SENDGRID_API_KEY=SG.your_actual_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
RESET_PASSWORD_BASE_URL=https://auth.tylerlovell.com/reset-password
SENDGRID_PASSWORD_RESET_TEMPLATE_ID=d-your_password_reset_template_id_here
SENDGRID_PASSWORD_CHANGED_TEMPLATE_ID=d-your_password_changed_template_id_here
```

### Option 2: Key Vault References (PRODUCTION - Most Secure)

For maximum security in production, use Azure Key Vault references in your configuration:

```json
{
  "SendGridApiKey": "@Microsoft.KeyVault(VaultName=your-keyvault;SecretName=SendGrid-ApiKey)",
  "SendGridFromEmail": "@Microsoft.KeyVault(VaultName=your-keyvault;SecretName=SendGrid-FromEmail)",
  "ResetPasswordBaseUrl": "@Microsoft.KeyVault(VaultName=your-keyvault;SecretName=Reset-Password-BaseUrl)",
  "SendGridPasswordResetTemplateId": "@Microsoft.KeyVault(VaultName=your-keyvault;SecretName=SendGrid-PasswordReset-TemplateId)",
  "SendGridPasswordChangedTemplateId": "@Microsoft.KeyVault(VaultName=your-keyvault;SecretName=SendGrid-PasswordChanged-TemplateId)"
}
```

### Option 3: Direct Values (DEVELOPMENT ONLY)

For development only, set values directly in `local.settings.json`:

```json
{
  "SendGridApiKey": "REPLACE_WITH_YOUR_SENDGRID_API_KEY",
  "SendGridFromEmail": "<EMAIL>",
  "ResetPasswordBaseUrl": "https://auth.tylerlovell.com/reset-password",
  "SendGridPasswordResetTemplateId": "d-your_password_reset_template_id_here",
  "SendGridPasswordChangedTemplateId": "d-your_password_changed_template_id_here"
}
```

### Configuration Priority

The service checks for configuration values in this order:

1. Configuration file values (e.g., `local.settings.json`)
2. Environment variables
3. **Required values must be explicitly set** - no defaults provided

### Local Development Setup

1. Copy `.env.example` to `.env`
2. Fill in your actual SendGrid API key
3. Update the from email to match your verified sender
4. Update the reset URL to match your application

## What SendGrid Handles vs Function

### SendGrid Handles:

- **Email Delivery**: Actual sending of emails
- **Sender Reputation**: IP warming, reputation management
- **Deliverability**: Spam filtering, bounce handling
- **Default Branding**: Can set default "From Name" in dashboard
- **Templates**: Optional advanced email templates with styling
- **Analytics**: Email open rates, click tracking, etc.

### Function Handles:

- **API Key Management**: Securely storing and using the API key
- **From Email Selection**: Which verified sender to use
- **Email Content**: Subject lines, body content, reset links
- **Reset URLs**: Application-specific password reset links
- **Logging**: Tracking email sending for debugging

## Security Best Practices

1. **Never commit API keys to source control**
2. **Use environment variables for production**
3. **Regularly rotate API keys**
4. **Monitor SendGrid usage and set up alerts**
5. **Use domain authentication instead of single sender when possible**
6. **Set up webhook endpoints for bounce/spam notifications**

## Testing

### Development Testing

- The service includes bypass mode when no API key is configured
- Check logs for reset links when in bypass mode
- Test with a valid API key to ensure actual email delivery

### Production Testing

1. Test password reset flow end-to-end
2. Check SendGrid activity dashboard for delivery status
3. Monitor bounce rates and spam reports
4. Verify emails arrive in inbox (not spam folder)

## Troubleshooting

### Common Issues:

1. **401 Authentication Error**: Check API key is correct and has Mail Send permissions
2. **403 Forbidden**: Verify sender email is authenticated in SendGrid
3. **Emails go to spam**: Set up domain authentication and SPF/DKIM records
4. **High bounce rate**: Clean your email list and verify addresses

### SendGrid Support:

- Documentation: https://docs.sendgrid.com
- Support: Available through SendGrid dashboard
- Status Page: https://status.sendgrid.com

## Cost Considerations

- **Free Tier**: 100 emails/day forever
- **Paid Plans**: Start at $19.95/month for 50,000 emails
- **Pay-as-you-go**: Available for variable usage
- Monitor usage in SendGrid dashboard to avoid overages
