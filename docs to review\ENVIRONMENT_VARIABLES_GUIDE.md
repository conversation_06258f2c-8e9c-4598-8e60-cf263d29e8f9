# Environment Variables Configuration Guide

## Overview

This guide covers all environment variables and configuration settings required for the invitation-based user management system.

## Required Environment Variables

### Azure Function App Configuration

#### Core Azure Settings

| Variable                      | Description                     | Example Value                                    | Required |
| ----------------------------- | ------------------------------- | ------------------------------------------------ | -------- |
| `AzureWebJobsStorage`         | Azure Storage connection string | `DefaultEndpointsProtocol=https;AccountName=...` | Yes      |
| `FUNCTIONS_WORKER_RUNTIME`    | Runtime environment             | `dotnet-isolated`                                | Yes      |
| `FUNCTIONS_EXTENSION_VERSION` | Functions runtime version       | `~4`                                             | Yes      |

#### Entra ID (Azure AD) Configuration

| Variable                         | Description                             | Example Value                          | Required |
| -------------------------------- | --------------------------------------- | -------------------------------------- | -------- |
| `EntraExternalID__ClientId`      | Azure AD App Registration Client ID     | `********-1234-1234-1234-************` | Yes      |
| `EntraExternalID__ClientSecret`  | Azure AD App Registration Client Secret | `abcdefghijklmnopqrstuvwxyz`           | Yes      |
| `EntraExternalID__TenantId`      | Azure AD Tenant ID                      | `********-4321-4321-4321-************` | Yes      |
| `EntraExternalID__DefaultDomain` | Default domain for user creation        | `yourtenant.onmicrosoft.com`           | Yes      |

#### SendGrid Email Configuration

| Variable                             | Description                       | Example Value                         | Required |
| ------------------------------------ | --------------------------------- | ------------------------------------- | -------- |
| `SendGrid__ApiKey`                   | SendGrid API key                  | `SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` | Yes      |
| `SendGrid__FromEmail`                | Verified sender email address     | `<EMAIL>`              | Yes      |
| `SendGrid__FromName`                 | Display name for sender           | `Your Application Name`               | Yes      |
| `SendGrid__UserInvitationTemplateId` | Invitation email template ID      | `d-************34567890************`  | Yes      |
| `SendGrid__AccountCreatedTemplateId` | Welcome email template ID         | `d-09876543************09********09`  | Yes      |
| `SendGrid__BypassMode`               | Disable email sending for testing | `false`                               | No       |

#### Storage Configuration

| Variable                    | Description                          | Example Value                                    | Required   |
| --------------------------- | ------------------------------------ | ------------------------------------------------ | ---------- |
| `Storage__ConnectionString` | Azure Blob Storage connection string | `DefaultEndpointsProtocol=https;AccountName=...` | Optional\* |

\*Note: If not provided, `AzureWebJobsStorage` will be used as fallback.

#### Rate Limiting Configuration

| Variable                                           | Description                     | Example Value | Required |
| -------------------------------------------------- | ------------------------------- | ------------- | -------- |
| `RateLimit__DefaultWindowMinutes`                  | Default rate limit window       | `15`          | No       |
| `RateLimit__DefaultMaxRequests`                    | Default max requests per window | `10`          | No       |
| `RateLimit__Endpoints__invite-user__MaxRequests`   | Invitation endpoint limit       | `5`           | No       |
| `RateLimit__Endpoints__invite-user__WindowMinutes` | Invitation window               | `15`          | No       |
| `RateLimit__Endpoints__register__MaxRequests`      | Registration endpoint limit     | `3`           | No       |
| `RateLimit__Endpoints__register__WindowMinutes`    | Registration window             | `15`          | No       |

#### Password Reset Configuration (Optional)

| Variable                              | Description                | Example Value                        | Required |
| ------------------------------------- | -------------------------- | ------------------------------------ | -------- |
| `PasswordReset__TokenExpirationHours` | Reset token expiration     | `24`                                 | No       |
| `PasswordReset__SendGridTemplateId`   | Password reset template ID | `**********************************` | No       |

## Local Development Configuration

### local.settings.json Template

```json
{
  "IsEncrypted": false,
  "Values": {
    // Azure Functions Core Settings
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    "FUNCTIONS_EXTENSION_VERSION": "~4",

    // Entra ID Configuration
    "EntraExternalID__ClientId": "your-client-id-here",
    "EntraExternalID__ClientSecret": "your-client-secret-here",
    "EntraExternalID__TenantId": "your-tenant-id-here",
    "EntraExternalID__DefaultDomain": "yourtenant.onmicrosoft.com",

    // SendGrid Configuration
    "SendGrid__ApiKey": "SG.your-sendgrid-api-key-here",
    "SendGrid__FromEmail": "<EMAIL>",
    "SendGrid__FromName": "Your Application Name",
    "SendGrid__UserInvitationTemplateId": "d-your-invitation-template-id",
    "SendGrid__AccountCreatedTemplateId": "d-your-welcome-template-id",
    "SendGrid__BypassMode": "true",

    // Storage Configuration (optional - uses AzureWebJobsStorage if not specified)
    "Storage__ConnectionString": "your-blob-storage-connection-string",

    // Rate Limiting Configuration (optional - uses defaults if not specified)
    "RateLimit__DefaultWindowMinutes": "15",
    "RateLimit__DefaultMaxRequests": "10",
    "RateLimit__Endpoints__invite-user__MaxRequests": "5",
    "RateLimit__Endpoints__invite-user__WindowMinutes": "15",
    "RateLimit__Endpoints__register__MaxRequests": "3",
    "RateLimit__Endpoints__register__WindowMinutes": "15",

    // Optional: Logging Configuration
    "Logging__LogLevel__Default": "Information",
    "Logging__LogLevel__PasswordHistoryValidator": "Debug"
  }
}
```

### Development Setup Steps

1. **Copy Template**

   ```bash
   cp local.settings.json.template local.settings.json
   ```

2. **Configure Azure Storage Emulator**

   - Install Azure Storage Emulator or Azurite
   - Use `"UseDevelopmentStorage=true"` for local development
   - Or provide actual Azure Storage connection string

3. **Set SendGrid Bypass Mode**
   ```json
   "SendGrid__BypassMode": "true"
   ```
   This prevents emails from being sent during development.

## Production Configuration

### Azure Portal Configuration

1. **Navigate to Function App**

   - Go to Azure Portal
   - Find your Function App
   - Go to Configuration → Application settings

2. **Add Required Settings**
   Copy all values from local.settings.json to Application settings.

3. **Use Azure Key Vault (Recommended)**
   ```
   @Microsoft.KeyVault(SecretUri=https://yourkeyvault.vault.azure.net/secrets/EntraClientSecret/)
   ```

### Azure Key Vault Setup

#### Secrets to Store in Key Vault

| Secret Name               | Description                     |
| ------------------------- | ------------------------------- |
| `EntraClientSecret`       | Azure AD Client Secret          |
| `SendGridApiKey`          | SendGrid API Key                |
| `StorageConnectionString` | Azure Storage Connection String |

#### Key Vault References

```json
{
  "EntraExternalID__ClientSecret": "@Microsoft.KeyVault(SecretUri=https://yourkeyvault.vault.azure.net/secrets/EntraClientSecret/)",
  "SendGrid__ApiKey": "@Microsoft.KeyVault(SecretUri=https://yourkeyvault.vault.azure.net/secrets/SendGridApiKey/)",
  "Storage__ConnectionString": "@Microsoft.KeyVault(SecretUri=https://yourkeyvault.vault.azure.net/secrets/StorageConnectionString/)"
}
```

### Environment-Specific Configurations

#### Development Environment

```json
{
  "SendGrid__BypassMode": "true",
  "Logging__LogLevel__Default": "Debug",
  "RateLimit__DefaultMaxRequests": "50"
}
```

#### Staging Environment

```json
{
  "SendGrid__BypassMode": "false",
  "SendGrid__FromEmail": "<EMAIL>",
  "Logging__LogLevel__Default": "Information",
  "RateLimit__DefaultMaxRequests": "20"
}
```

#### Production Environment

```json
{
  "SendGrid__BypassMode": "false",
  "SendGrid__FromEmail": "<EMAIL>",
  "Logging__LogLevel__Default": "Warning",
  "RateLimit__DefaultMaxRequests": "10"
}
```

## Configuration Validation

### Startup Validation

The application validates configuration at startup:

```csharp
// These will throw exceptions if missing or invalid
services.Configure<EntraOptions>(/* validation */);
services.Configure<SendGridOptions>(/* validation */);
services.Configure<StorageOptions>(/* validation */);
```

### Manual Validation

Use this PowerShell script to validate configuration:

```powershell
# Validate Azure Function App settings
$resourceGroup = "your-resource-group"
$functionAppName = "your-function-app"

$settings = az functionapp config appsettings list --name $functionAppName --resource-group $resourceGroup | ConvertFrom-Json

# Check required settings
$requiredSettings = @(
    "EntraExternalID__ClientId",
    "EntraExternalID__ClientSecret",
    "EntraExternalID__TenantId",
    "SendGrid__ApiKey",
    "SendGrid__FromEmail",
    "SendGrid__UserInvitationTemplateId",
    "SendGrid__AccountCreatedTemplateId"
)

foreach ($setting in $requiredSettings) {
    $value = ($settings | Where-Object { $_.name -eq $setting }).value
    if ([string]::IsNullOrEmpty($value)) {
        Write-Warning "Missing required setting: $setting"
    } else {
        Write-Host "✓ $setting is configured" -ForegroundColor Green
    }
}
```

## Security Considerations

### Secret Management

1. **Never commit secrets to source control**

   - Use `.gitignore` for `local.settings.json`
   - Use environment variables or Key Vault

2. **Rotate secrets regularly**

   - Azure AD Client Secrets: Every 2 years max
   - SendGrid API Keys: Every 6 months
   - Storage Keys: Every year

3. **Use Managed Identity when possible**
   ```csharp
   // For Azure services, prefer Managed Identity
   var credential = new DefaultAzureCredential();
   ```

### Access Control

1. **Principle of Least Privilege**

   - Grant minimum required permissions
   - Use separate service accounts

2. **Monitor Access**
   - Enable audit logging
   - Review access logs regularly

## Troubleshooting

### Common Configuration Issues

#### "Connection string not found"

- Check `AzureWebJobsStorage` is set
- Verify Storage connection string format
- Ensure storage account exists and is accessible

#### "SendGrid authentication failed"

- Verify API key is correct and active
- Check API key permissions (Mail Send required)
- Ensure from email is verified in SendGrid

#### "Azure AD authentication failed"

- Verify Client ID, Secret, and Tenant ID
- Check Azure AD app registration configuration
- Ensure app has required Microsoft Graph permissions

### Diagnostic Commands

#### Test Azure Storage Connection

```bash
# Using Azure CLI
az storage account show --name yourstorageaccount --resource-group yourresourcegroup
```

#### Test SendGrid API Key

```bash
# Using curl
curl -X GET "https://api.sendgrid.com/v3/user/account" \
  -H "Authorization: Bearer SG.your-api-key"
```

#### Test Azure AD Authentication

```bash
# Using Azure CLI
az login --service-principal -u your-client-id -p your-client-secret --tenant your-tenant-id
```

## Configuration Templates

### Docker Development

```dockerfile
ENV EntraExternalID__ClientId=your-client-id
ENV EntraExternalID__ClientSecret=your-client-secret
ENV EntraExternalID__TenantId=your-tenant-id
ENV SendGrid__ApiKey=your-sendgrid-key
ENV SendGrid__BypassMode=true
```

### Azure Resource Manager Template

```json
{
  "type": "Microsoft.Web/sites/config",
  "apiVersion": "2021-02-01",
  "name": "[concat(parameters('functionAppName'), '/appsettings')]",
  "properties": {
    "EntraExternalID__ClientId": "[parameters('entraClientId')]",
    "EntraExternalID__ClientSecret": "[parameters('entraClientSecret')]",
    "SendGrid__ApiKey": "[parameters('sendGridApiKey')]",
    "SendGrid__FromEmail": "[parameters('fromEmail')]"
  }
}
```

### Terraform Configuration

```hcl
resource "azurerm_function_app" "invitation_system" {
  app_settings = {
    "EntraExternalID__ClientId"     = var.entra_client_id
    "EntraExternalID__ClientSecret" = var.entra_client_secret
    "SendGrid__ApiKey"              = var.sendgrid_api_key
    "SendGrid__FromEmail"           = var.from_email
  }
}
```

## Monitoring Configuration

### Application Insights

```json
{
  "APPINSIGHTS_INSTRUMENTATIONKEY": "your-app-insights-key",
  "APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=your-key;IngestionEndpoint=...",
  "Logging__ApplicationInsights__LogLevel__Default": "Information"
}
```

### Log Analytics Queries

```kusto
// Monitor configuration errors
traces
| where message contains "configuration" or message contains "setting"
| where severityLevel >= 3
| project timestamp, message, severityLevel
| order by timestamp desc
```

For additional configuration support, refer to the Azure Functions and SendGrid documentation.
