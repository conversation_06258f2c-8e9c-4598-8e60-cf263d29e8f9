# Configuration Guide

## Simple Configuration Approach

**Local Development:**
- `local.settings.json` - All settings (including temporary secrets for local dev)
- `User Secrets` - For real secrets you don't want in source control

**Production (Azure):**
- **App Settings** - Non-sensitive configuration (URLs, feature flags, etc.)
- **Key Vault** - Sensitive secrets (API keys, connection strings, etc.)

## Configuration Priority Order

1. **Azure Key Vault** (highest priority)
2. **Environment Variables** (App Settings in Azure portal)
3. **User Secrets** (local development only)
4. **local.settings.json** (lowest priority)

## Local Development Setup

### local.settings.json
All settings for local development:

```json
{
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    "EntraExternalID:ClientId": "your-client-id",
    "EntraExternalID:ClientSecret": "temp-local-secret",
    "SendGrid:ApiKey": "temp-local-key"
  }
}
```

### User Secrets (optional for local dev)
For real secrets during development:

```bash
dotnet user-secrets set "EntraExternalID:ClientSecret" "real-secret"
dotnet user-secrets set "SendGrid:ApiKey" "real-api-key"
```

## Production Setup

- **App Settings**: Set in Azure portal for non-sensitive config
- **Key Vault**: Automatically loaded for secrets when `KeyVaultUrl` is configured

No complex fallback logic needed - it's just local vs production.