.send-invitation-container {
    max-width: 600px;
}

.send-invitation-container h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background-color: var(--primary-red);
    margin: 1rem auto;
}

#sendButton {
    background-color: var(--primary-red);
    color: var(--color-white);
    border: 2px solid var(--primary-red);
}

#sendButton:hover {
    background-color: var(--primary-red-dark);
    border-color: var(--primary-red-dark);
}

.border-top {
    border-top: 1px solid var(--gray-border) !important;
    padding-top: 1.5rem !important;
    margin-top: 1.5rem !important;
}

#recentInvitations {
    font-size: 0.875rem;
    color: var(--gray-medium);
    line-height: 1.5;
    padding: 1rem;
    background-color: var(--gray-light);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--primary-red);
}

.recent-invitation-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: var(--gray-light);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-red);
}

.recent-invitation-item .invitation-status.sent {
    background-color: #dbeafe;
    color: #1e40af;
}

.recent-invitation-item .invitation-status.error {
    background-color: var(--primary-red-light);
    color: var(--primary-red);
}

.col-md-10 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.row.justify-content-center {
    margin-left: 0;
    margin-right: 0;
}

#sendButton.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: var(--color-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
