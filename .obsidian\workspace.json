{"main": {"id": "e9ac41c5ed21f187", "type": "split", "children": [{"id": "1d13ee0151c53a57", "type": "tabs", "children": [{"id": "0b1d566cffc5123b", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}]}], "direction": "vertical"}, "left": {"id": "d2b4f994ab6f37b0", "type": "split", "children": [{"id": "779b046fa5b69029", "type": "tabs", "children": [{"id": "d5bc9ec40b2bcf22", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "9c30fb2bb2bc7a0b", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "1decc83a0b516cb8", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "ca3b7ea4be1373be", "type": "split", "children": [{"id": "f978ae2461e281f2", "type": "tabs", "children": [{"id": "8af248df1e2fcf05", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks"}}, {"id": "5fcb0bcc4051bbd1", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links"}}, {"id": "b9b23f8bd473e83e", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "9a15c6a0365488f3", "type": "leaf", "state": {"type": "outline", "state": {"followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "0b1d566cffc5123b", "lastOpenFiles": ["docs/TESTING/Configuration Validation Test Guide.md", "docs/TESTING", "docs/IMPLEMENTATION/Configuration Standardization Implementation.md", "docs/IMPLEMENTATION", "Shared/ConfigurationValidator.cs", "docs/ASSESSMENTS/Error Handling and Debug Logging Analysis.md", "bin/Release/net8.0/PowerPagesCustomAuth.pdb", "obj/Release/net8.0/ref/PowerPagesCustomAuth.dll", "bin/Release/net8.0/PowerPagesCustomAuth.dll", "bin/Release/net8.0/PowerPagesCustomAuth.runtimeconfig.json", "obj/Release/net8.0/PowerPagesCustomAuth.genruntimeconfig.cache", "bin/Release/net8.0/PowerPagesCustomAuth.deps.json", "bin/Release/net8.0/extensions.json", "docs/Current Architecture Summary.md", "docs/AuthenticationFunction Removal.md", "docs/Testing Strategy.md", "docs/ASSESSMENTS/Entra External ID Native Password Policy Capabilities Analysis.md", "docs/ARCHIVE/ARCHIVE_NOTE.md", "docs/ASSESSMENTS/ARCHIVE_NOTE.md", "docs/Configuration Variables Reference.md", "docs/ASSESSMENTS/PowerPagesCustomAuth Password Policy Comprehensive Analysis.md", "docs/Entra External ID Native Password Policy Capabilities Analysis.md", "docs/Azure Functions Authentication System Application Separation Analysis - August 14th 2024.md", "docs/ASSESSMENTS/Azure Functions Authentication System Application Separation Analysis - August 14th 2024.md", "docs/TODO/CORS Solution.md", "docs/Production Deployment Setup Guide.md", "docs/PowerPagesCustomAuth Configuration Variables Reference.md", "docs/Implementation Validation Checklist.md", "docs/Password Policy Full Compliance Implementation Summary.md", "docs/CORS Solution.md", "docs/Password Policy Compliance Analysis.md", "docs/System Overview.md"]}