using System.ComponentModel.DataAnnotations;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// SendGrid email service configuration options.
/// All properties are required and validated at startup.
/// </summary>
public class SendGridOptions
{
    public const string SectionName = "SendGrid";

    [Required(ErrorMessage = "SendGrid:ApiKey is required")]
    public string ApiKey { get; set; } = string.Empty;

    [Required(ErrorMessage = "SendGrid:FromEmail is required")]
    [EmailAddress(ErrorMessage = "SendGrid:FromEmail must be a valid email address")]
    public string FromEmail { get; set; } = string.Empty;

    [Required(ErrorMessage = "SendGrid:PasswordResetTemplateId is required")]
    public string PasswordResetTemplateId { get; set; } = string.Empty;

    [Required(ErrorMessage = "SendGrid:PasswordChangedTemplateId is required")]
    public string PasswordChangedTemplateId { get; set; } = string.Empty;

    [Required(ErrorMessage = "SendGrid:UserInvitationTemplateId is required")]
    public string UserInvitationTemplateId { get; set; } = string.Empty;

    [Required(ErrorMessage = "SendGrid:AccountCreatedTemplateId is required")]
    public string AccountCreatedTemplateId { get; set; } = string.Empty;

    [Required(ErrorMessage = "SendGrid:PasswordExpirationTemplateId is required")]
    public string PasswordExpirationTemplateId { get; set; } = string.Empty;

    [Required(ErrorMessage = "SendGrid:PasswordExpiredTemplateId is required")]
    public string PasswordExpiredTemplateId { get; set; } = string.Empty;
}


/// <summary>
/// Entra External ID (Azure AD B2C) configuration options.
/// All properties are required and validated at startup.
/// </summary>
public class EntraOptions
{
    public const string SectionName = "EntraExternalID";

    [Required(ErrorMessage = "EntraExternalID:TenantId is required")]
    public string TenantId { get; set; } = string.Empty;

    [Required(ErrorMessage = "EntraExternalID:ClientId is required")]
    public string ClientId { get; set; } = string.Empty;

    [Required(ErrorMessage = "EntraExternalID:ClientSecret is required")]
    public string ClientSecret { get; set; } = string.Empty;

    [Required(ErrorMessage = "EntraExternalID:DefaultDomain is required")]
    public string DefaultDomain { get; set; } = string.Empty;
}


/// <summary>
/// Password reset URL configuration options.
/// All properties are required and validated at startup.
/// </summary>
public class PasswordResetOptions
{
    public const string SectionName = "PasswordReset";

    [Required(ErrorMessage = "PasswordReset:BaseUrl is required")]
    [Url(ErrorMessage = "PasswordReset:BaseUrl must be a valid URL")]
    public string BaseUrl { get; set; } = string.Empty;
}

/// <summary>
/// Account registration URL configuration options.
/// All properties are required and validated at startup.
/// </summary>
public class AccountRegistrationOptions
{
    public const string SectionName = "AccountRegistration";

    [Required(ErrorMessage = "AccountRegistration:BaseUrl is required")]
    [Url(ErrorMessage = "AccountRegistration:BaseUrl must be a valid URL")]
    public string BaseUrl { get; set; } = string.Empty;
}

/// <summary>
/// Rate limiting configuration options.
/// Uses default values if not specified - no validation required.
/// </summary>
public class RateLimitOptions
{
    public const string SectionName = "RateLimit";

    [Range(1, 1000, ErrorMessage = "RateLimit:MaxRequestsPerMinute must be between 1 and 1000")]
    public int MaxRequestsPerMinute { get; set; } = 60;
}

/// <summary>
/// Azure Storage configuration options.
/// Connection string is required and validated at startup.
/// </summary>
public class StorageOptions
{
    public const string SectionName = "Storage";

    [Required(ErrorMessage = "Storage connection string is required. Configure AzureWebJobsStorage or Storage:ConnectionString")]
    public string ConnectionString { get; set; } = string.Empty;
}

/// <summary>
/// User invitation configuration options.
/// Uses default values if not specified - no validation required.
/// </summary>
public class InvitationOptions
{
    public const string SectionName = "Invitation";

    [Range(1, 365, ErrorMessage = "Invitation:TokenExpirationDays must be between 1 and 365")]
    public int TokenExpirationDays { get; set; } = 45;
}
