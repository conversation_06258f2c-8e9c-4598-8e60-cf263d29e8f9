# Production Deployment Guide

Complete guide for deploying the PowerPages Custom Authentication system to production environments.

## **Pre-Deployment Checklist**

### **1. Security Configuration** ⚠️ **CRITICAL**

- [ ] **Change Authorization Level from Anonymous to Function**
  ```csharp
  // In AuthenticationService.cs, PasswordService.cs, UtilityService.cs
  [HttpTrigger(AuthorizationLevel.Function, "post", "options", Route = null)]
  ```

- [ ] **Configure CORS for Production**
  ```json
  // In host.json
  "cors": {
    "allowedOrigins": ["https://your-actual-domain.powerappsportals.com"],
    "allowedMethods": ["GET", "POST", "OPTIONS"],
    "allowedHeaders": ["Content-Type", "Authorization", "x-functions-key", "X-Requested-With", "X-Client-Version"]
  }
  ```

- [ ] **Secure Configuration Storage**
  - Move all secrets to Azure Key Vault
  - Remove placeholder values from configuration
  - Validate all connection strings

### **2. Environment Preparation**

- [ ] **Azure Resources Created**
  - Azure Function App (Production)
  - Azure Storage Account
  - Azure Key Vault
  - Application Insights instance

- [ ] **Entra External ID Configuration**
  - Production app registration created
  - Redirect URIs updated for production domain
  - API permissions granted and admin consented

- [ ] **SMTP2GO Configuration**
  - Production API key generated
  - Domain authentication completed
  - Sender email verified

## **Deployment Process**

### **Step 1: Prepare Local Environment**

1. **Update Configuration**
   ```bash
   # Update local.settings.json with production values
   # Ensure all placeholder values are replaced
   ```

2. **Build Release Version**
   ```bash
   dotnet clean --configuration Release
   dotnet build --configuration Release
   dotnet publish --configuration Release
   ```

3. **Run Pre-Deployment Tests**
   ```bash
   # Run unit tests
   dotnet test --configuration Release --filter Category=Unit
   
   # Run integration tests (if available)
   dotnet test --configuration Release --filter Category=Integration
   ```

### **Step 2: Deploy via VS Code Azure Functions Extension**

1. **Open VS Code**
   - Ensure Azure Functions extension is installed
   - Sign in to Azure account

2. **Deploy Function App**
   - Press `Ctrl+Shift+P` (Windows) or `Cmd+Shift+P` (Mac)
   - Type "Azure Functions: Deploy to Function App"
   - Select your production Function App
   - Confirm deployment

3. **Monitor Deployment**
   - Watch output window for deployment progress
   - Verify no errors during deployment
   - Note the deployment completion message

### **Step 3: Configure Production Settings**

1. **Navigate to Azure Portal**
   - Go to your Function App
   - Select "Configuration" → "Application settings"

2. **Add Required Settings**
   ```json
   {
     "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=...",
     "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
     "EntraExternalID:TenantId": "your-production-tenant-id",
     "EntraExternalID:ClientId": "your-production-client-id",
     "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/ClientSecret/)",
     "SMTP2GOApiKey": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/SMTP2GOApiKey/)",
     "SMTP2GOFromEmail": "<EMAIL>",
     "SMTP2GOFromName": "Your Application Name",
     "ResetPasswordBaseUrl": "https://your-production-domain.powerappsportals.com",
     "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=...",
     "PasswordHistory:MaxCount": "12",
     "PasswordHistory:WorkFactor": "12",
     "RateLimit:MaxRequestsPerMinute": "60",
     "ApplicationName": "Your Production Application Name"
   }
   ```

3. **Configure Key Vault Access**
   - Enable System Assigned Managed Identity for Function App
   - Grant Function App access to Key Vault secrets
   - Verify Key Vault references resolve correctly

### **Step 4: Post-Deployment Verification**

1. **Function Health Check**
   ```bash
   # Test UtilityService health endpoint
   curl "https://your-function-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_FUNCTION_KEY"
   ```

2. **Configuration Validation**
   ```bash
   # Test configuration endpoint
   curl "https://your-function-app.azurewebsites.net/api/UtilityService?operation=config-check&code=YOUR_FUNCTION_KEY"
   ```

3. **End-to-End Testing**
   - Test user registration flow
   - Test forgot password flow
   - Test password reset flow
   - Verify email delivery

## **Power Pages Configuration Update**

### **Update Site Settings**

1. **Navigate to Power Pages Admin**
   - Go to Power Pages Management Portal
   - Select your production site
   - Go to "Setup" → "Site Settings"

2. **Update Function URL**
   ```
   Setting Name: AzureFunctionUrl
   Setting Value: https://your-function-app.azurewebsites.net/api
   ```

3. **Add Function Key (if using Function authorization)**
   ```
   Setting Name: AzureFunctionKey
   Setting Value: YOUR_PRODUCTION_FUNCTION_KEY
   ```

### **Update Custom Pages**

1. **Update JavaScript Configuration**
   - Ensure all Power Pages files reference production URLs
   - Update any hardcoded development URLs
   - Test all custom authentication flows

## **Rollback Procedures**

### **Emergency Rollback**

1. **Immediate Actions**
   ```bash
   # Stop Function App
   az functionapp stop --name your-function-app --resource-group your-rg
   
   # Revert to previous deployment slot (if configured)
   az functionapp deployment slot swap --name your-function-app --resource-group your-rg --slot staging --target-slot production
   ```

2. **Revert Power Pages Settings**
   - Change AzureFunctionUrl back to previous working version
   - Restore previous configuration values

### **Planned Rollback**

1. **Deploy Previous Version**
   - Use VS Code to deploy previous working version
   - Update configuration settings as needed
   - Verify functionality

2. **Validate Rollback**
   - Run health checks
   - Test critical user flows
   - Monitor for errors

## **Common Deployment Issues**

### **Issue 1: Function Key Authentication Failures**

**Symptoms**: 401 Unauthorized errors after deployment

**Solution**:
```bash
# Get function key from Azure Portal
# Update Power Pages configuration with correct key
# Verify CORS settings allow your domain
```

### **Issue 2: Key Vault Access Denied**

**Symptoms**: Configuration values not resolving, 500 errors

**Solution**:
```bash
# Verify Managed Identity is enabled
# Check Key Vault access policies
# Validate Key Vault reference format
```

### **Issue 3: Email Delivery Failures**

**Symptoms**: Password reset emails not sending

**Solution**:
```bash
# Verify SMTP2GO API key is correct
# Check domain authentication status
# Validate sender email verification
```

### **Issue 4: CORS Errors**

**Symptoms**: Browser console shows CORS errors

**Solution**:
```json
// Update host.json with correct domain
"allowedOrigins": ["https://your-actual-domain.powerappsportals.com"]
```

## **Deployment Validation Checklist**

### **Functional Testing**
- [ ] User registration works
- [ ] Password validation enforces history
- [ ] Forgot password sends emails
- [ ] Password reset completes successfully
- [ ] All error handling works correctly

### **Security Testing**
- [ ] Function authorization is enabled
- [ ] CORS is properly configured
- [ ] Secrets are stored in Key Vault
- [ ] No sensitive data in logs

### **Performance Testing**
- [ ] Response times are acceptable
- [ ] Rate limiting is working
- [ ] Concurrent user handling is adequate

### **Monitoring Testing**
- [ ] Application Insights is collecting data
- [ ] Health checks are responding
- [ ] Error logging is working
- [ ] Alerts are configured

## **Post-Deployment Tasks**

1. **Monitor Initial Usage**
   - Watch Application Insights for errors
   - Monitor email delivery rates
   - Check performance metrics

2. **Update Documentation**
   - Record production URLs and settings
   - Update operational procedures
   - Document any deployment-specific configurations

3. **Schedule Maintenance**
   - Set up regular health checks
   - Plan for certificate renewals
   - Schedule security reviews

## **Next Steps**

After successful deployment:
1. Set up comprehensive monitoring (see Monitoring and Alerting Setup Guide)
2. Implement automated health checks
3. Configure backup and disaster recovery procedures
4. Plan for scaling and performance optimization
