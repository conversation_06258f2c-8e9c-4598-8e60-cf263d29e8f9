using Microsoft.Extensions.Configuration;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Provides environment detection capabilities for Azure Functions.
/// Used to determine appropriate configuration validation strategies.
/// </summary>
public static class EnvironmentDetector
{
    /// <summary>
    /// Determines if the application is running in a production environment.
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <returns>True if running in production environment</returns>
    public static bool IsProduction(IConfiguration configuration)
    {
        // Check multiple environment indicators
        var environment = configuration["AZURE_FUNCTIONS_ENVIRONMENT"] ?? 
                         configuration["ASPNETCORE_ENVIRONMENT"] ?? 
                         configuration["ENVIRONMENT"] ??
                         "Development";
        
        return environment.Equals("Production", StringComparison.OrdinalIgnoreCase);
    }
    
    /// <summary>
    /// Determines if the application is running in Azure (vs local development).
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <returns>True if running in Azure environment</returns>
    public static bool IsAzureHosted(IConfiguration configuration)
    {
        // Azure Functions sets WEBSITE_SITE_NAME when running in Azure
        return !string.IsNullOrEmpty(configuration["WEBSITE_SITE_NAME"]);
    }
    
    /// <summary>
    /// Determines if the application is running locally for development.
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <returns>True if running in local development environment</returns>
    public static bool IsLocalDevelopment(IConfiguration configuration)
    {
        return !IsAzureHosted(configuration);
    }
    
    /// <summary>
    /// Gets a descriptive name for the current environment.
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <returns>Environment description for logging and error messages</returns>
    public static string GetEnvironmentDescription(IConfiguration configuration)
    {
        if (IsProduction(configuration))
        {
            return "Production (Azure)";
        }
        else if (IsAzureHosted(configuration))
        {
            return "Development/Staging (Azure)";
        }
        else
        {
            return "Local Development";
        }
    }
    
    /// <summary>
    /// Determines if Key Vault should be required for sensitive configuration.
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <returns>True if Key Vault should be required for sensitive data</returns>
    public static bool ShouldRequireKeyVault(IConfiguration configuration)
    {
        // Require Key Vault in production or when explicitly configured
        return IsProduction(configuration) || IsAzureHosted(configuration);
    }
}
