# Configuration Validation Test Guide

## Overview

This guide provides test scenarios to verify the fail-fast configuration validation is working correctly across all Azure Functions services.

## Test Scenarios

### **1. Missing Required Configuration Test**

**Purpose:** Verify application fails at startup with clear error messages when required configuration is missing.

**Test Steps:**
1. **Backup current configuration:**
   ```bash
   cp local.settings.json local.settings.json.backup
   ```

2. **Remove required configuration:**
   ```json
   {
     "Values": {
       "AzureWebJobsStorage": "UseDevelopmentStorage=true",
       "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated"
       // Remove all other configuration
     }
   }
   ```

3. **Start application:**
   ```bash
   func start
   ```

4. **Expected Result:**
   Application should fail to start with error message listing all missing required configuration values.

### **2. Placeholder Value Detection Test**

**Purpose:** Verify application detects and rejects placeholder configuration values.

**Test Steps:**
1. **Set placeholder values in local.settings.json:**
   ```json
   {
     "Values": {
       "EntraExternalID:TenantId": "REPLACE_WITH_YOUR_ACTUAL_TENANT_ID",
       "EntraExternalID:ClientSecret": "temp-local-dev-secret",
       "SendGrid:ApiKey": "REPLACE_WITH_YOUR_SENDGRID_API_KEY",
       "KeyVaultUrl": "https://your-keyvault-name.vault.azure.net/"
     }
   }
   ```

2. **Start application:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should fail with specific error messages about placeholder values.

### **3. Valid Configuration Test**

**Purpose:** Verify application starts successfully with proper configuration.

**Test Steps:**
1. **Restore valid configuration:**
   ```bash
   cp local.settings.json.backup local.settings.json
   ```

2. **Ensure all required values are set:**
   - Verify no placeholder values remain
   - Confirm all required configuration sections are present

3. **Start application:**
   ```bash
   func start
   ```

4. **Expected Result:**
   Application should start successfully without configuration errors.

### **4. Optional Configuration Warning Test**

**Purpose:** Verify warnings are logged for optional configuration values outside recommended ranges.

**Test Steps:**
1. **Set extreme values for optional configuration:**
   ```json
   {
     "Values": {
       "PasswordHistory:MaxCount": "100",
       "PasswordHistory:WorkFactor": "5",
       "RateLimit:MaxRequestsPerMinute": "5000"
     }
   }
   ```

2. **Start application and check logs:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should start but log warnings about values outside recommended ranges.

### **5. Key Vault Reference Validation Test**

**Purpose:** Verify Key Vault URL is required when Key Vault references are used.

**Test Steps:**
1. **Add Key Vault reference without URL:**
   ```json
   {
     "Values": {
       "SendGrid:ApiKey": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/SendGridApiKey/)",
       "KeyVaultUrl": ""
     }
   }
   ```

2. **Start application:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should fail with error about missing KeyVaultUrl when Key Vault references are detected.

## Email Service Specific Tests

### **6. SendGrid Template ID Validation Test**

**Purpose:** Verify all SendGrid template IDs are validated at startup.

**Test Steps:**
1. **Set invalid template IDs:**
   ```json
   {
     "Values": {
       "SendGrid:PasswordResetTemplateId": "d-placeholder-template-id",
       "SendGrid:PasswordChangedTemplateId": "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
     }
   }
   ```

2. **Start application:**
   ```bash
   func start
   ```

3. **Expected Result:**
   Application should fail with specific errors about invalid template IDs.

### **7. Email Service Runtime Test**

**Purpose:** Verify EmailService no longer has null client checks at runtime.

**Test Steps:**
1. **Start application with valid configuration**
2. **Trigger email sending operation** (password reset, registration, etc.)
3. **Check logs for email operations**

**Expected Result:**
- No "SendGrid client not configured" error messages
- Email operations should either succeed or fail with actual SendGrid API errors
- No null reference exceptions

## Production Deployment Tests

### **8. Azure Function App Configuration Test**

**Purpose:** Verify configuration validation works in Azure environment.

**Test Steps:**
1. **Deploy to Azure Function App**
2. **Remove required application setting** (e.g., SendGrid:ApiKey)
3. **Monitor Function App logs**

**Expected Result:**
Function App should fail to start with configuration validation errors in Application Insights logs.

### **9. Key Vault Integration Test**

**Purpose:** Verify Key Vault references work with validation.

**Test Steps:**
1. **Configure Azure Key Vault with secrets**
2. **Set Key Vault references in Function App settings:**
   ```
   SendGrid:ApiKey = @Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/SendGridApiKey/)
   KeyVaultUrl = https://vault.vault.azure.net/
   ```
3. **Deploy and start Function App**

**Expected Result:**
Function App should start successfully and resolve Key Vault references.

## Troubleshooting Common Issues

### **Configuration Not Loading**
- Verify `local.settings.json` is in the correct format
- Check for JSON syntax errors
- Ensure file is not corrupted

### **Key Vault Access Issues**
- Verify managed identity is configured
- Check Key Vault access policies
- Confirm Key Vault URL format is correct

### **Template ID Format Issues**
- SendGrid template IDs should start with "d-"
- Template IDs should be exactly 32 characters after "d-"
- Verify templates exist in SendGrid account

## Expected Error Message Examples

### **Missing Configuration:**
```
Required configuration values are missing or invalid:

• EntraExternalID:TenantId is required. Set your Azure AD B2C tenant ID.
• SendGrid:ApiKey is required and must be a valid API key. Configure your SendGrid API key in application settings or Azure Key Vault.

Please configure these values in your application settings, local.settings.json, or Azure Key Vault.
```

### **Placeholder Values:**
```
Required configuration values are missing or invalid:

• EntraExternalID:ClientSecret is required. Set your Azure AD B2C application client secret (consider using Azure Key Vault).
• KeyVaultUrl contains placeholder value. Set your actual Azure Key Vault URL.
```

### **Optional Configuration Warnings:**
```
warn: ConfigurationValidation[0]
      PasswordHistory:WorkFactor value 5 is outside recommended range (10-15). Using value anyway.
warn: ConfigurationValidation[0]
      RateLimit:MaxRequestsPerMinute value 5000 is outside recommended range (1-1000). Using value anyway.
```

## Success Criteria

✅ **All required configuration missing:** Application fails to start with comprehensive error message
✅ **Placeholder values detected:** Application fails with specific placeholder error messages  
✅ **Valid configuration:** Application starts successfully without errors
✅ **Optional warnings:** Warnings logged for values outside recommended ranges
✅ **Key Vault validation:** Proper validation of Key Vault URL when references are used
✅ **Runtime reliability:** No configuration-related runtime failures
✅ **Clear error messages:** All error messages provide actionable guidance
