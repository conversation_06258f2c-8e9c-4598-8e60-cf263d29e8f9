using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using BCrypt.Net;

namespace PasswordHistoryValidator;

public partial class PasswordFunction : BaseFunctionService
{
    private readonly ILogger<PasswordFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly RateLimitService _rateLimitService;
    private readonly ResetTokenManager _resetTokenManager;
    private readonly IEmailService _emailService;
    private readonly GraphServiceClient _graphServiceClient;

    public PasswordFunction(
        ILogger<PasswordFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        RateLimitService rateLimitService,
        ResetTokenManager resetTokenManager,
        IEmailService emailService,
        GraphServiceClient graphServiceClient,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitService = rateLimitService;
        _resetTokenManager = resetTokenManager;
        _emailService = emailService;
        _graphServiceClient = graphServiceClient;
    }


    /// validate, change, reset-request, reset-confirm, reset-verify

    [Function("PasswordService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        // Handle CORS preflight requests
        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "validate" => await HandlePasswordValidation(req, correlationId, cancellationToken),
                "update-history" => await HandleHistoryUpdate(req, correlationId, cancellationToken),
                "reset-initiate" => await HandleResetInitiate(req, correlationId, cancellationToken),
                "reset-complete" => await HandleResetComplete(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Password service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }



    private async Task<HttpResponseData> HandlePasswordValidation(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<PasswordRequest>(req.Body, JsonOptions, cancellationToken);

        if (data == null)
            return await CreateErrorResponse(req, "Invalid request data", correlationId);

        var applicationName = data.ApplicationName ?? "Default Application";

        // Validate request data
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(data);
        if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
        {
            var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
            return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
        }

        // Rate limiting
        var clientId = BaseFunctionService.GetClientIdentifier(req);
        var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "validate", cancellationToken);
        if (!rateLimitInfo.IsAllowed)
        {
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = "Rate limit exceeded. Please try again later.",
                errorCode = "RateLimitExceeded",
                retryAfter = rateLimitInfo.WindowResetTime
            }, HttpStatusCode.TooManyRequests, correlationId);
        }

        if (string.IsNullOrEmpty(data.UserId) || string.IsNullOrEmpty(data.NewPassword))
            return await CreateErrorResponse(req, "UserId and NewPassword are required for validation", correlationId);

        // Validate password against history
        var validationResult = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
            applicationName, data.UserId, data.NewPassword, cancellationToken);

        if (!validationResult.IsSuccess)
        {
            if (validationResult.ErrorCode == ErrorCodes.PasswordInHistory)
            {
                _logger.LogWarning("Password reuse detected for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                return await CreateJsonResponse(req, new
                {
                    version = "1.0.0",
                    action = "ShowBlockPage",
                    userMessage = validationResult.ErrorMessage
                }, HttpStatusCode.Conflict, correlationId);
            }
            else
            {
                _logger.LogError("Password validation failed for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    data.Email, validationResult.ErrorMessage, correlationId);
                return await CreateErrorResponse(req, "Error accessing password history. Please try again later.", correlationId);
            }
        }

        // Update password history
        var updateResult = await _passwordHistoryService.UpdatePasswordHistoryAsync(
            applicationName, data.UserId, data.NewPassword, cancellationToken);

        if (!updateResult.IsSuccess)
        {
            _logger.LogError("Failed to update password history for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                data.Email, updateResult.ErrorMessage, correlationId);
            return await CreateJsonResponse(req, new
            {
                version = "1.0.0",
                action = "Continue",
                userMessage = "Password validation successful. History update will be retried.",
                metadata = new
                {
                    email = data.Email,
                    validationTimestamp = DateTime.UtcNow,
                    historyUpdated = false
                }
            }, HttpStatusCode.OK, correlationId);
        }

        return await CreateJsonResponse(req, new
        {
            version = "1.0.0",
            action = "Continue",
            userMessage = "Password validation successful",
            metadata = new
            {
                email = data.Email,
                validationTimestamp = DateTime.UtcNow,
                historyUpdated = true
            }
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task<HttpResponseData> HandleHistoryUpdate(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<UpdatePasswordHistoryRequest>(req.Body, JsonOptions, cancellationToken);

        if (data == null)
            return await CreateErrorResponse(req, "Invalid request data", correlationId);

        // Validate request data
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(data);
        if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
        {
            var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
            return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
        }

        // Rate limiting
        var clientId = BaseFunctionService.GetClientIdentifier(req);
        var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "update-history", cancellationToken);
        if (!rateLimitInfo.IsAllowed)
        {
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = "Rate limit exceeded. Please try again later.",
                errorCode = "RateLimitExceeded",
                retryAfter = rateLimitInfo.WindowResetTime
            }, HttpStatusCode.TooManyRequests, correlationId);
        }

        var userId = !string.IsNullOrEmpty(data.UserId) ? data.UserId : data.Email;

        // Update password history
        var updateResult = await _passwordHistoryService.UpdatePasswordHistoryAsync(
            data.ApplicationName, userId, data.NewPassword, cancellationToken);

        if (!updateResult.IsSuccess)
        {
            _logger.LogError("Failed to update password history for user {UserId}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                userId, updateResult.ErrorMessage, correlationId);
            return await CreateErrorResponse(req, updateResult.ErrorMessage, correlationId);
        }

        return await CreateJsonResponse(req, new { success = true, message = "Password history updated successfully" }, HttpStatusCode.OK, correlationId);
    }

    private async Task<HttpResponseData> HandleResetInitiate(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<PasswordRequest>(req.Body, JsonOptions, cancellationToken);

        if (data == null)
            return await CreateErrorResponse(req, "Invalid request data", correlationId);

        // Validate request data
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(data);
        if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
        {
            var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
            return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
        }

        // Rate limiting
        var clientId = BaseFunctionService.GetClientIdentifier(req);
        var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "reset-initiate", cancellationToken);
        if (!rateLimitInfo.IsAllowed)
        {
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = "Rate limit exceeded. Please try again later.",
                errorCode = "RateLimitExceeded",
                retryAfter = rateLimitInfo.WindowResetTime
            }, HttpStatusCode.TooManyRequests, correlationId);
        }

        // Check if user exists in application
        var emailEsc = ODataHelpers.EscapeString(data.Email ?? string.Empty);
        var appEsc = ODataHelpers.EscapeString(data.ApplicationName ?? string.Empty);
        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter =
                    $"(mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')) and department eq '{appEsc}'";
                requestConfiguration.QueryParameters.Select = new[] { "id" };
                requestConfiguration.QueryParameters.Top = 1;
            }, cancellationToken);
        var userExists = users?.Value?.Count > 0;

        if (userExists)
        {
            _ = Task.Run(async () => await SendResetEmailAsync(data, correlationId));
        }

        return await CreateJsonResponse(req, new
        {
            success = true,
            message = "If an account with that email exists, you will receive a password reset link shortly. Please check your email."
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task SendResetEmailAsync(PasswordRequest data, string correlationId)
    {
        try
        {
            var resetToken = _resetTokenManager.GenerateSecureToken();
            var verificationCode = _resetTokenManager.StoreResetToken(data.ApplicationName, data.Email!, resetToken);
            await _resetTokenManager.SendResetEmail(data.Email!, resetToken, verificationCode, data.ApplicationName, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending reset email to {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
        }
    }

    private async Task<HttpResponseData> HandleResetComplete(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<PasswordRequest>(req.Body, JsonOptions, cancellationToken);

        if (data == null)
            return await CreateErrorResponse(req, "Invalid request data", correlationId);

        // Determine operation type
        bool hasToken = !string.IsNullOrEmpty(data.Token);
        bool hasVerificationCode = !string.IsNullOrEmpty(data.VerificationCode);
        if (!(hasToken && hasVerificationCode))
            return await CreateErrorResponse(req, "Token and verification code are required for password reset", correlationId);

        var applicationName = data.ApplicationName ?? "Default Application";

        // Validate token for reset operations
        var (isValid, tokenData, errorMessage) = _resetTokenManager.ValidateResetToken(data.Token!, data.VerificationCode!);
        if (!isValid)
        {
            _logger.LogWarning("Invalid or expired reset token/verification code for application {ApplicationName} [CorrelationId: {CorrelationId}]",
                applicationName, correlationId);
            return await CreateErrorResponse(req, errorMessage, correlationId);
        }
        data.Email = tokenData!.Email;

        // Rate limiting
        var clientId = BaseFunctionService.GetClientIdentifier(req);
        var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "reset-complete", cancellationToken);
        if (!rateLimitInfo.IsAllowed)
        {
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = "Rate limit exceeded. Please try again later.",
                errorCode = "RateLimitExceeded",
                retryAfter = rateLimitInfo.WindowResetTime
            }, HttpStatusCode.TooManyRequests, correlationId);
        }

        // Resolve user by email
        string userEmail = data.Email!;
        var emailEsc2 = ODataHelpers.EscapeString(userEmail);
        var appEsc2 = ODataHelpers.EscapeString(applicationName);
        var filter = $"(mail eq '{emailEsc2}' or userPrincipalName eq '{emailEsc2}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc2}')) and department eq '{appEsc2}'";

        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter = filter;
                requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                requestConfiguration.QueryParameters.Top = 1;
            }, cancellationToken);

        var user = users?.Value?.FirstOrDefault();
        if (user == null)
        {
            _logger.LogWarning("Failed to resolve email {Email} to User ID for application {ApplicationName} [CorrelationId: {CorrelationId}]",
                userEmail, applicationName, correlationId);
            return await CreateErrorResponse(req, "User not found with the provided email address in the specified application context.", correlationId);
        }

        string resolvedUserId = user.Id ?? string.Empty;

        if (string.IsNullOrEmpty(data.NewPassword))
            return await CreateErrorResponse(req, "NewPassword is required", correlationId);

        // Validate new password against history
        var passwordValidation = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
            applicationName, resolvedUserId, data.NewPassword, cancellationToken);

        if (!passwordValidation.IsSuccess)
        {
            _logger.LogWarning("Password validation failed for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                userEmail, passwordValidation.ErrorMessage, correlationId);
            return await CreateErrorResponse(req, passwordValidation.ErrorMessage, correlationId);
        }

        // Update password in Entra External ID
        var userUpdate = new User
        {
            PasswordProfile = new PasswordProfile
            {
                Password = data.NewPassword,
                ForceChangePasswordNextSignIn = false
            }
        };
        await _graphServiceClient.Users[resolvedUserId].PatchAsync(userUpdate, requestConfiguration => { }, cancellationToken);

        // Mark token as used 
        try
        {
            if (!string.IsNullOrEmpty(data.Token))
            {
                _resetTokenManager.MarkTokenAsUsed(data.Token);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to mark reset token as used for {Email} [CorrelationId: {CorrelationId}]", userEmail, correlationId);
        }

        // Handle post-password-update tasks in background (email + history update)
        _ = Task.Run(async () => await HandlePostPasswordUpdateTasksAsync(data, userEmail, resolvedUserId, correlationId, cancellationToken));

        var successMessage = "Password reset successfully. You will be logged out and need to sign in with your new password.";

        return await CreateJsonResponse(req, new
        {
            message = successMessage,
            email = userEmail,
            requiresLogout = true
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task HandlePostPasswordUpdateTasksAsync(PasswordRequest data, string userEmail, string resolvedUserId, string correlationId, CancellationToken cancellationToken)
    {
        // Update password history
        try
        {
            var historyUpdate = await _passwordHistoryService.UpdatePasswordHistoryAsync(
                data.ApplicationName, resolvedUserId, data.NewPassword ?? string.Empty, cancellationToken);

            if (!historyUpdate.IsSuccess)
            {
                _logger.LogError("Password updated but failed to update password history for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    userEmail, historyUpdate.ErrorMessage, correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating password history for {Email} [CorrelationId: {CorrelationId}]", userEmail, correlationId);
        }

        // Send password changed notification
        try
        {
            await _emailService.SendPasswordChangedNotificationAsync(userEmail, correlationId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending password changed notification to {Email} [CorrelationId: {CorrelationId}]", userEmail, correlationId);
        }

    }

}
