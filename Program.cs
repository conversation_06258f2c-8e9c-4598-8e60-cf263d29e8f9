using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Azure.Storage.Blobs;
using Azure.Identity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Graph;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using System.Text.Json;
using Microsoft.Azure.Functions.Worker;


var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureAppConfiguration((context, builder) =>
    {
        builder.AddEnvironmentVariables()
               .AddUserSecrets<Program>();

        var settings = builder.Build();

        var keyVaultUrl = settings.GetValue<string>("KeyVaultUrl");
        if (!string.IsNullOrEmpty(keyVaultUrl))
        {
            builder.AddAzureKeyVault(
                new Uri(keyVaultUrl),
                new DefaultAzureCredential(
                    new DefaultAzureCredentialOptions
                    {
                        ManagedIdentityClientId = settings.GetValue<string>("UserAssignedClientId")
                    }));
        }
    })
    .ConfigureServices((context, services) =>
    {
        var configuration = context.Configuration;

        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();

        services.AddMemoryCache();
        services.AddSingleton<IConfiguration>(configuration);

        services.Configure<SendGridOptions>(configuration.GetSection(SendGridOptions.SectionName));
        services.Configure<EntraOptions>(configuration.GetSection(EntraOptions.SectionName));
        services.Configure<PasswordResetOptions>(configuration.GetSection(PasswordResetOptions.SectionName));
        services.Configure<AccountRegistrationOptions>(configuration.GetSection(AccountRegistrationOptions.SectionName));
        services.Configure<StorageOptions>(options =>
        {
            options.ConnectionString = configuration["AzureWebJobsStorage"] ?? configuration["Storage:ConnectionString"] ?? string.Empty;
        });
        services.Configure<RateLimitOptions>(configuration.GetSection(RateLimitOptions.SectionName));
        services.Configure<InvitationOptions>(configuration.GetSection(InvitationOptions.SectionName));

        // Blob Storage client for password history
        services.AddSingleton<BlobServiceClient>(serviceProvider =>
        {
            var storageOptions = serviceProvider.GetRequiredService<IOptions<StorageOptions>>();
            var connectionString = storageOptions.Value.ConnectionString
                ?? throw new InvalidOperationException("Storage connection string is required. Configure AzureWebJobsStorage or Storage:ConnectionString.");

            return new BlobServiceClient(connectionString);
        });

        services.AddHttpClient("default")
                .AddStandardResilienceHandler(options =>
                {
                    options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(30);
                    options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(15);
                });

        services.AddHttpClient();

        services.AddScoped<IPasswordHistoryService, PasswordHistoryService>();
        services.AddScoped<IEmailService, EmailService>();

        // Graph API client for Entra External ID
        services.AddScoped<GraphServiceClient>(serviceProvider =>
        {
            var entraOptions = serviceProvider.GetRequiredService<IOptions<EntraOptions>>();
            var options = entraOptions.Value;
            
            if (string.IsNullOrEmpty(options.ClientId))
                throw new InvalidOperationException("EntraExternalID:ClientId is required");
            if (string.IsNullOrEmpty(options.ClientSecret))
                throw new InvalidOperationException("EntraExternalID:ClientSecret is required");
            if (string.IsNullOrEmpty(options.TenantId))
                throw new InvalidOperationException("EntraExternalID:TenantId is required");

            var credential = new ClientSecretCredential(options.TenantId, options.ClientId, options.ClientSecret);
            return new GraphServiceClient(credential);
        });

        // Utility and token management
        services.AddSingleton<RateLimitService>();
        services.AddScoped<ResetTokenManager>();
        services.AddScoped<InvitationTokenManager>();

        // Functions
        services.AddScoped<PasswordHistoryValidator.PasswordFunction>();
        services.AddScoped<PasswordHistoryValidator.UtilityFunction>();
        services.AddScoped<PasswordHistoryValidator.InvitationFunction>();
        services.AddScoped<PasswordHistoryValidator.RegistrationFunction>();

        services.AddSingleton<JsonSerializerOptions>(provider => new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        });
    })
    .Build();

host.Run();
