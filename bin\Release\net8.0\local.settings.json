{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "PasswordHistory:MaxCount": "12", "PasswordHistory:WorkFactor": "12", "RateLimit:MaxRequestsPerMinute": "60", "KeyVaultUrl": "https://your-keyvault-name.vault.azure.net/", "ApplicationName": "Password Reset Service", "EntraExternalID:TenantId": "REPLACE_WITH_YOUR_ACTUAL_TENANT_ID", "EntraExternalID:ClientId": "REPLACE_WITH_YOUR_ACTUAL_CLIENT_ID", "EntraExternalID:ClientSecret": "temp-local-dev-secret", "EntraExternalID:DefaultDomain": "yourtenant.onmicrosoft.com", "SendGrid:ApiKey": "temp-local-dev-key", "SendGrid:FromEmail": "<EMAIL>", "SendGrid:PasswordResetTemplateId": "d-dc912d5057e84d46a0b1e402ededde15", "SendGrid:PasswordChangedTemplateId": "d-1ca6c61c4cbf46c7ab74a5086b5af8ac", "SendGrid:UserInvitationTemplateId": "d-cb6d9203442b4fb7874b095408af66fc", "SendGrid:AccountCreatedTemplateId": "d-b78332752a114a3a98b64a528eb83bcc", "SendGrid:PasswordExpirationTemplateId": "d-placeholder-expiration-template-id", "SendGrid:PasswordExpiredTemplateId": "d-placeholder-expired-template-id", "PasswordReset:BaseUrl": "https://auth.tylerlovell.com/reset-password", "AccountRegistration:BaseUrl": "https://site-dccs0.powerappsportals.com/Custom-Account-Registration", "Invitation:TokenExpirationDays": "45", "PASSWORD_EXPIRATION_DAYS": "90", "PASSWORD_WARNING_DAYS": "15", "Storage:ConnectionString": "UseDevelopmentStorage=true"}, "Host": {"LocalHttpPort": 7071, "CORS": "*", "CORSCredentials": false}}