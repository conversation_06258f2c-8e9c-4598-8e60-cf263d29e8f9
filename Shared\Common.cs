using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PasswordHistoryValidator.Shared;

public class Result<T>
{
    public bool IsSuccess { get; private set; }
    public T? Value { get; private set; }
    public string ErrorMessage { get; private set; } = string.Empty;
    public string? ErrorCode { get; private set; }

    public static Result<T> Success(T value) => new()
    {
        IsSuccess = true,
        Value = value
    };

    public static Result<T> Failure(string errorMessage) => new()
    {
        IsSuccess = false,
        ErrorMessage = errorMessage
    };

    public static Result<T> Failure(string errorMessage, string errorCode) => new()
    {
        IsSuccess = false,
        ErrorMessage = errorMessage,
        ErrorCode = errorCode
    };
}


public static class ErrorCodes
{
    public const string InvalidRequest = "INVALID_REQUEST";
    public const string PasswordInHistory = "PASSWORD_IN_HISTORY";
    public const string StorageError = "STORAGE_ERROR";
    public const string ConfigurationError = "CONFIGURATION_ERROR";
    public const string ValidationError = "VALIDATION_ERROR";
    public const string RateLimitExceeded = "RATE_LIMIT_EXCEEDED";
    public const string UnauthorizedAccess = "UNAUTHORIZED_ACCESS";
    public const string UserNotFound = "USER_NOT_FOUND";
    public const string InternalError = "INTERNAL_ERROR";
    public const string AuthenticationFailed = "AUTHENTICATION_FAILED";
    public const string UserAlreadyExists = "USER_ALREADY_EXISTS";
    public const string InvalidToken = "INVALID_TOKEN";
    public const string TokenExpired = "TOKEN_EXPIRED";
    public const string EmailSendFailed = "EMAIL_SEND_FAILED";
}

public class UpdatePasswordHistoryRequest
{
    [JsonPropertyName("email")]
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public required string Email { get; set; }

    [JsonPropertyName("newPassword")]
    [Required(ErrorMessage = "New password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public required string NewPassword { get; set; }

    [JsonPropertyName("userId")]
    public string? UserId { get; set; }

    [JsonPropertyName("applicationName")]
    [Required(ErrorMessage = "Application name is required")]
    public required string ApplicationName { get; set; }

    [JsonPropertyName("correlationId")]
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}
