:root {
    --primary-red: #8B1538;
    --primary-red-dark: #6B1028;
    --primary-red-light: #f8f1f3;
    --color-black: #000000;
    --color-white: #ffffff;
    --gray-dark: #374151;
    --gray-medium: #6b7280;
    --gray-light: #f3f4f6;
    --gray-border: #e5e7eb;
    --font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

body {
    font-family: var(--font-primary);
    color: var(--gray-dark);
    line-height: 1.6;
    background-color: var(--gray-light);
}

h1, h2, h3, h4, h5, h6 {
    color: var(--color-black);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 2px;
    margin-bottom: var(--spacing-md);
}

h2 {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
}

h3 {
    font-size: 1.5rem;
    color: var(--primary-red);
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--gray-dark);
}

a {
    color: var(--primary-red);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-red-dark);
    text-decoration: underline;
}

.columnBlockLayout {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-width: 250px;
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.sectionBlockLayout {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    padding: var(--spacing-sm);
    background-color: var(--color-white);
}

.container-flex {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.forgot-password-container,
.login-container,
.reset-password-container,
.send-invitation-container {
    max-width: 600px;
    margin: var(--spacing-lg) auto;
    padding: var(--spacing-xl);
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border-top: 4px solid var(--primary-red);
}

.forgot-password-container h1,
.login-container h1,
.reset-password-container h1,
.send-invitation-container h1 {
    text-align: center;
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.forgot-password-container h1::after,
.login-container h1::after,
.reset-password-container h1::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background-color: var(--primary-red);
    margin: var(--spacing-sm) auto;
}

.form-group {
    margin-bottom: var(--spacing-md);
}

label {
    display: block;
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: var(--spacing-xs);
    font-size: 0.95rem;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
textarea,
select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--gray-border);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-family: var(--font-primary);
    background-color: var(--color-white);
    color: var(--gray-dark);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-red);
    box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1);
}

input[type="text"]:invalid,
input[type="email"]:invalid,
input[type="password"]:invalid {
    border-color: var(--primary-red);
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 120px;
}

.btn-primary,
button[type="submit"],
input[type="submit"] {
    background-color: var(--primary-red);
    color: var(--color-white);
    border: 2px solid var(--primary-red);
}

.btn-primary:hover,
button[type="submit"]:hover,
input[type="submit"]:hover {
    background-color: var(--primary-red-dark);
    border-color: var(--primary-red-dark);
    color: var(--color-white);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-red);
    border: 2px solid var(--primary-red);
}

.btn-secondary:hover {
    background-color: var(--primary-red);
    color: var(--color-white);
    text-decoration: none;
}

.btn-tertiary {
    background-color: transparent;
    color: var(--gray-dark);
    border: 2px solid var(--gray-border);
}

.btn-tertiary:hover {
    background-color: var(--gray-light);
    border-color: var(--gray-medium);
    color: var(--gray-dark);
    text-decoration: none;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.875rem;
    min-width: 100px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.125rem;
    min-width: 150px;
}

.btn-block {
    width: 100%;
    display: block;
}

.message,
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    border-left: 4px solid;
    font-weight: 500;
}

.message-success,
.alert-success {
    background-color: #f0f9ff;
    border-left-color: var(--primary-red);
    color: var(--gray-dark);
}

.message-error,
.alert-error,
.alert-danger {
    background-color: var(--primary-red-light);
    border-left-color: var(--primary-red);
    color: var(--primary-red-dark);
}

.message-warning,
.alert-warning {
    background-color: #fef3c7;
    border-left-color: #f59e0b;
    color: #92400e;
}

.message-info,
.alert-info {
    background-color: #eff6ff;
    border-left-color: #3b82f6;
    color: #1e40af;
}

.message-hidden {
    display: none;
}

.card {
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.card-header {
    padding: var(--spacing-md);
    background-color: var(--gray-light);
    border-bottom: 1px solid var(--gray-border);
    font-weight: 600;
    color: var(--color-black);
}

.card-body {
    padding: var(--spacing-md);
}

.card-footer {
    padding: var(--spacing-md);
    background-color: var(--gray-light);
    border-top: 1px solid var(--gray-border);
    text-align: center;
}

.verification-code-container {
    background-color: var(--gray-light);
    border-left: 4px solid var(--primary-red);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    text-align: center;
    border-radius: var(--radius-md);
}

.verification-code {
    background-color: var(--color-white);
    border: 2px solid var(--primary-red);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin: var(--spacing-sm) 0;
    font-family: 'Courier New', monospace;
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-red);
    letter-spacing: 6px;
    display: inline-block;
    min-width: 200px;
}

.flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-red {
    color: var(--primary-red);
}

.text-black {
    color: var(--color-black);
}

.text-gray {
    color: var(--gray-medium);
}

.bg-red {
    background-color: var(--primary-red);
}

.bg-white {
    background-color: var(--color-white);
}

.bg-gray-light {
    background-color: var(--gray-light);
}

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }

@media (max-width: 768px) {
    .columnBlockLayout {
        padding: var(--spacing-md);
        margin: var(--spacing-md) 0;
    }

    .forgot-password-container,
    .login-container,
    .reset-password-container {
        margin: var(--spacing-sm) auto;
        padding: var(--spacing-md);
        max-width: 95%;
    }

    h1 {
        font-size: 2rem;
        letter-spacing: 1px;
    }

    h2 {
        font-size: 1.5rem;
    }

    .verification-code {
        font-size: 1.5rem;
        letter-spacing: 4px;
        min-width: 150px;
    }

    .btn {
        padding: 14px 20px;
        font-size: 0.95rem;
    }

    .container-flex {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .forgot-password-container,
    .login-container,
    .reset-password-container {
        padding: var(--spacing-sm);
        margin: var(--spacing-xs) auto;
    }

    h1 {
        font-size: 1.75rem;
    }

    .verification-code {
        font-size: 1.25rem;
        letter-spacing: 2px;
        min-width: 120px;
    }
}

.entityform .form-group {
    margin-bottom: var(--spacing-md);
}

.entityform label {
    font-weight: 600;
    color: var(--color-black);
    margin-bottom: var(--spacing-xs);
}

.entityform input[type="text"],
.entityform input[type="email"],
.entityform input[type="password"],
.entityform textarea,
.entityform select {
    border: 2px solid var(--gray-border);
    border-radius: var(--radius-md);
    padding: 12px 16px;
}

.entityform input[type="text"]:focus,
.entityform input[type="email"]:focus,
.entityform input[type="password"]:focus,
.entityform textarea:focus,
.entityform select:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 3px rgba(139, 21, 56, 0.1);
}

.navbar-brand {
    color: var(--color-black) !important;
    font-weight: 300;
    letter-spacing: 2px;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: var(--gray-dark) !important;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-red) !important;
}

.footer {
    background-color: var(--color-black);
    color: var(--color-white);
    padding: var(--spacing-lg) 0;
    margin-top: var(--spacing-xl);
}

.footer a {
    color: var(--color-white);
}

.footer a:hover {
    color: var(--primary-red);
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

*:focus {
    outline: 2px solid var(--primary-red);
    outline-offset: 2px;
}

@media (prefers-contrast: high) {
    :root {
        --primary-red: #8B1538;
        --gray-border: #666666;
    }
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
