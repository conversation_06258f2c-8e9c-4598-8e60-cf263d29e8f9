{"version": "2.0.0", "tasks": [{"label": "clean (functions)", "command": "dotnet", "args": ["clean", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "type": "process", "problemMatcher": "$msCompile"}, {"label": "build (functions)", "command": "dotnet", "args": ["build", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "type": "process", "dependsOn": "clean (functions)", "group": {"kind": "build", "isDefault": true}, "problemMatcher": "$msCompile"}, {"label": "clean release (functions)", "command": "dotnet", "args": ["clean", "--configuration", "Release", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "type": "process", "problemMatcher": "$msCompile"}, {"label": "publish (functions)", "command": "dotnet", "args": ["publish", "--configuration", "Release", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "type": "process", "dependsOn": "clean release (functions)", "problemMatcher": "$msCompile"}, {"type": "func", "dependsOn": "build (functions)", "options": {"cwd": "${workspaceFolder}/bin/Debug/net8.0"}, "command": "host start", "isBackground": true, "problemMatcher": "$func-dotnet-watch"}]}