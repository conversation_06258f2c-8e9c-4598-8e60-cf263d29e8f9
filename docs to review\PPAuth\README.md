# PowerPages Custom Authentication System

A hybrid authentication system that combines standard Entra External ID login with custom password history validation for compliance requirements.

## 🏗️ **System Architecture**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Power Pages   │───▶│  Azure Functions │───▶│ Entra External  │
│   (Frontend)    │    │   (Backend)      │    │      ID         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │ Azure Blob      │              │
         └──────────────│ Storage         │──────────────┘
                        │ (Password       │
                        │  History)       │
                        └─────────────────┘
```

## 🔧 **Core Components**

### **Power Pages (Frontend)**

- Custom registration, forgot password, and reset password forms
- Standard Entra External ID login (no custom code needed)
- JavaScript integration with Azure Functions

### **Azure Functions (Backend)**

- **AuthenticationService**: User registration and management
- **PasswordService**: Password operations with history validation
- **UtilityService**: Health checks and system maintenance

### **Supporting Services (Simplified Architecture)**

- **PasswordOperations**: Password change operations and validation
- **UserResolution**: Email-to-UserID mapping and user lookups
- **ResetTokenManager**: Token generation, storage, and validation
- **ConfigurationHelper**: Static configuration validation methods
- **RateLimitHelper**: Simple rate limiting protection

### **Entra External ID**

- Handles standard user authentication and sessions
- User storage and management
- Self-service sign-up disabled to enforce password history

### **Azure Blob Storage**

- Stores encrypted password history (12 passwords per user)
- Application-isolated data storage
- Reset token storage

## 🔐 **Security Features**

- **12-Password History**: Prevents password reuse
- **BCrypt Encryption**: Work factor 12 (4,096 iterations)
- **Application Isolation**: Multi-tenant password history
- **Rate Limiting**: Prevents brute force attacks
- **SMTP2GO Integration**: Secure email delivery

## 🚀 **User Flows**

### **Registration**

1. User fills custom registration form
2. Password validated against complexity rules
3. User created in Entra External ID
4. Password hash stored in history
5. User redirected to login

### **Login**

1. User redirected to standard Entra External ID login
2. No custom validation needed
3. Power Pages handles session management

### **Password Reset**

1. User enters email on forgot password page
2. 6-digit verification code sent via SMTP2GO
3. User enters code and new password
4. Password validated against history
5. Password updated in Entra External ID and history

## 📋 **Quick Status Check**

### **Current Configuration**

- ✅ **Authorization Level**: Function (Properly Secured)
- ⚠️ **CORS**: Configure for specific domains in production
- ✅ **Email Service**: SMTP2GO configured
- ✅ **Password History**: 12 passwords, BCrypt work factor 12
- ✅ **Rate Limiting**: 60 requests per minute

### **Production Requirements**

- ✅ Authorization properly configured (Function level)
- 🔒 Configure CORS for specific domains
- 🔒 Move secrets to Azure Key Vault
- 📊 Set up monitoring and alerting

## 🛠️ **Development**

### **Local Setup**

```bash
# Clone and build
dotnet restore
dotnet build

# Configure local.settings.json with your values
# Run locally
func start
```

### **Key Configuration**

```json
{
  "EntraExternalID": {
    "TenantId": "your-tenant-id",
    "ClientId": "your-client-id",
    "ClientSecret": "your-client-secret"
  },
  "SendGrid": {
    "ApiKey": "SG.your-sendgrid-key",
    "FromEmail": "<EMAIL>"
  },
  "Storage": {
    "ConnectionString": "your-storage-connection"
  }
}
```

**📧 Email Setup**: See [SendGrid Setup Guide](../SENDGRID_SETUP_GUIDE.md) for detailed email configuration.

## 🚨 **Before Production**

1. **Security Hardening**

   - Change authorization levels from Anonymous to Function
   - Configure CORS for specific domains only
   - Move all secrets to Azure Key Vault

2. **Monitoring Setup**

   - Configure Application Insights
   - Set up health check alerts
   - Create performance dashboards

3. **Testing**
   - Test all user flows end-to-end
   - Verify email delivery
   - Validate security configurations

## 📚 **Documentation**

- **Quick Start Guide**: Get up and running fast
- **Security Essentials**: Critical security configurations
- **API Reference**: Complete endpoint documentation
- **Configuration Reference**: All settings explained
- **Troubleshooting Guide**: Common issues and solutions

## 🔗 **Key Endpoints**

### **AuthenticationService**

- `POST /api/AuthenticationService?operation=validate-credentials`
- `POST /api/AuthenticationService?operation=list-duplicates`

### **RegistrationService**

- `POST /api/RegistrationService?operation=register`

### **PasswordService**

- `POST /api/PasswordService?operation=validate`
- `POST /api/PasswordService?operation=reset-initiate`
- `POST /api/PasswordService?operation=reset-complete`

### **UtilityService**

- `GET /api/UtilityService?operation=health`
- `GET /api/UtilityService?operation=config-check`
- `GET /api/UtilityService?operation=stats`

## ⚡ **Quick Commands**

```bash
# Health check
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=health"

# Configuration validation
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=config-check"

# Deploy via VS Code
# Use Azure Functions extension: "Deploy to Function App"
```

## 📚 **Documentation**

- **[Architecture Overview](Architecture%20Overview.md)** - Simplified system architecture
- **[Quick Start Guide](Quick%20Start%20Guide.md)** - Get running in 30 minutes
- **[API Reference](API%20Reference.md)** - Complete endpoint documentation
- **[Configuration Reference](Configuration%20Reference.md)** - All settings explained
- **[Security Essentials](Security%20Essentials.md)** - Critical security setup
- **[Troubleshooting Guide](Troubleshooting%20Guide.md)** - Common issues and solutions
- **[Refactoring Summary](Refactoring%20Summary.md)** - Recent simplification changes

## 📞 **Support**

For issues or questions:

1. Check the Troubleshooting Guide
2. Review Application Insights logs
3. Use UtilityService health endpoints for diagnostics

---

**✅ Security**: This system is properly configured with Function-level authorization. Review the Security Essentials guide for additional production hardening steps.
