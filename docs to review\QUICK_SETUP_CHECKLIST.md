# Quick Setup Checklist

## Pre-Deployment Checklist

### ✅ Azure Resources

- [ ] **Azure Function App** created
- [ ] **Azure Storage Account** created
- [ ] **Azure AD App Registration** configured
- [ ] **Application Insights** enabled (optional)
- [ ] **Azure Key Vault** created (recommended)

### ✅ Azure AD App Registration

- [ ] **Client ID** obtained
- [ ] **Client Secret** created (expires in 2 years max)
- [ ] **Microsoft Graph API Permissions** granted:
  - [ ] `User.ReadWrite.All` (Application permission)
  - [ ] `Directory.ReadWrite.All` (Application permission)
- [ ] **Admin consent** granted for permissions
- [ ] **Authentication** configured (if needed for web app)

### ✅ SendGrid Setup

- [ ] **SendGrid account** created
- [ ] **API Key** generated with Mail Send permissions
- [ ] **Domain authentication** completed OR **Single sender verification** done
- [ ] **User Invitation Template** created and published
- [ ] **Account Created Template** created and published
- [ ] **Template IDs** copied for configuration

### ✅ Environment Variables

**Required Variables:**

- [ ] `EntraExternalID__ClientId`
- [ ] `EntraExternalID__ClientSecret`
- [ ] `EntraExternalID__TenantId`
- [ ] `EntraExternalID__DefaultDomain`
- [ ] `SendGrid__ApiKey`
- [ ] `SendGrid__FromEmail`
- [ ] `SendGrid__FromName`
- [ ] `SendGrid__UserInvitationTemplateId`
- [ ] `SendGrid__AccountCreatedTemplateId`
- [ ] `AzureWebJobsStorage`

## Quick Configuration Values

### local.settings.json Template

```json
{
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    "EntraExternalID__ClientId": "YOUR_CLIENT_ID",
    "EntraExternalID__ClientSecret": "YOUR_CLIENT_SECRET",
    "EntraExternalID__TenantId": "YOUR_TENANT_ID",
    "EntraExternalID__DefaultDomain": "yourtenant.onmicrosoft.com",
    "SendGrid__ApiKey": "SG.YOUR_SENDGRID_API_KEY",
    "SendGrid__FromEmail": "<EMAIL>",
    "SendGrid__FromName": "Your App Name",
    "SendGrid__UserInvitationTemplateId": "d-YOUR_INVITATION_TEMPLATE_ID",
    "SendGrid__AccountCreatedTemplateId": "d-YOUR_WELCOME_TEMPLATE_ID",
    "SendGrid__BypassMode": "true"
  }
}
```

## Quick Test Commands

### Test Invitation Send

```bash
curl -X POST "https://localhost:7071/api/InvitationService?operation=invite-user" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "firstName": "Test",
    "lastName": "User",
    "applicationId": "TestApp",
    "invitedBy": "<EMAIL>"
  }'
```

### Test Registration

```bash
curl -X POST "https://localhost:7071/api/RegistrationService?operation=register" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "YOUR_INVITATION_TOKEN",
    "verificationCode": "YOUR_VERIFICATION_CODE",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "firstName": "Test",
    "lastName": "User",
    "applicationName": "TestApp"
  }'
```

## Common Issues Quick Fix

| Issue                            | Quick Fix                                    |
| -------------------------------- | -------------------------------------------- |
| "SendGrid authentication failed" | Check API key format starts with `SG.`       |
| "Template not found"             | Verify template ID starts with `d-`          |
| "Azure AD authentication failed" | Check Client ID/Secret/Tenant ID             |
| "Storage connection failed"      | Verify storage connection string             |
| "From email not verified"        | Complete SendGrid domain/sender verification |

## Deployment Commands

### Azure CLI Deployment

```bash
# Set variables
RESOURCE_GROUP="your-rg"
FUNCTION_APP="your-function-app"

# Deploy function app
func azure functionapp publish $FUNCTION_APP

# Set required app settings
az functionapp config appsettings set --name $FUNCTION_APP --resource-group $RESOURCE_GROUP --settings \
  "EntraExternalID__ClientId=YOUR_CLIENT_ID" \
  "SendGrid__ApiKey=YOUR_SENDGRID_KEY" \
  "SendGrid__BypassMode=false"
```

### PowerShell Deployment

```powershell
# Publish function app
func azure functionapp publish your-function-app

# Configure settings via portal or ARM template
```

## URLs for Documentation

- **Full Setup Guide**: `docs/INVITATION_SYSTEM_GUIDE.md`
- **SendGrid Setup**: `docs/SENDGRID_SETUP_GUIDE.md`
- **Environment Variables**: `docs/ENVIRONMENT_VARIABLES_GUIDE.md`
- **SendGrid Templates**: `Services/Templates/`

## Support Contacts

- **Azure Functions**: [Azure Documentation](https://docs.microsoft.com/azure/azure-functions/)
- **SendGrid**: [SendGrid Support](https://support.sendgrid.com/)
- **Microsoft Graph**: [Graph Documentation](https://docs.microsoft.com/graph/)

---

**Last Updated**: January 2025
**Version**: 1.0
