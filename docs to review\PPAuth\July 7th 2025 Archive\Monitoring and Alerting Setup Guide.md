# Monitoring and Alerting Setup Guide

Comprehensive guide for setting up production monitoring, alerting, and observability for the PowerPages Custom Authentication system.

## **Overview**

This guide covers:
- Application Insights configuration
- Custom dashboards and metrics
- Alert rules for critical failures
- Log analysis and queries
- Performance monitoring
- Automated health checks

## **Application Insights Setup**

### **1. Create Application Insights Resource**

1. **Azure Portal Setup**
   ```bash
   # Create Application Insights resource
   az monitor app-insights component create \
     --app your-auth-app-insights \
     --location eastus \
     --resource-group your-resource-group \
     --application-type web
   ```

2. **Get Instrumentation Key**
   ```bash
   # Get the instrumentation key
   az monitor app-insights component show \
     --app your-auth-app-insights \
     --resource-group your-resource-group \
     --query instrumentationKey
   ```

### **2. Configure Function App Integration**

1. **Add Application Settings**
   ```json
   {
     "APPINSIGHTS_INSTRUMENTATIONKEY": "your-instrumentation-key",
     "APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=your-key;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/",
     "ApplicationInsightsAgent_EXTENSION_VERSION": "~3"
   }
   ```

2. **Verify host.json Configuration**
   ```json
   {
     "version": "2.0",
     "logging": {
       "applicationInsights": {
         "samplingSettings": {
           "isEnabled": true,
           "excludedTypes": "Request"
         }
       },
       "logLevel": {
         "default": "Information",
         "Function": "Information",
         "PasswordHistoryValidator": "Information"
       }
     }
   }
   ```

## **Custom Dashboards**

### **1. Authentication System Overview Dashboard**

Create a dashboard with these tiles:

#### **System Health Tile**
```kusto
// UtilityService health check results
requests
| where name contains "UtilityService"
| where url contains "operation=health"
| summarize 
    HealthChecks = count(),
    SuccessRate = avg(case(resultCode == "200", 1.0, 0.0)) * 100
    by bin(timestamp, 5m)
| render timechart
```

#### **Authentication Operations Tile**
```kusto
// Authentication service operations
requests
| where name contains "AuthenticationService"
| summarize 
    Registrations = countif(url contains "operation=register"),
    Logins = countif(url contains "operation=login"),
    Validations = countif(url contains "operation=validate-credentials")
    by bin(timestamp, 1h)
| render columnchart
```

#### **Password Operations Tile**
```kusto
// Password service operations
requests
| where name contains "PasswordService"
| summarize 
    Validations = countif(url contains "operation=validate"),
    ResetInitiations = countif(url contains "operation=reset-initiate"),
    ResetCompletions = countif(url contains "operation=reset-complete")
    by bin(timestamp, 1h)
| render columnchart
```

### **2. Error Analysis Dashboard**

#### **Error Rate Tile**
```kusto
// Overall error rate
requests
| summarize 
    TotalRequests = count(),
    ErrorRequests = countif(resultCode >= "400"),
    ErrorRate = (countif(resultCode >= "400") * 100.0) / count()
    by bin(timestamp, 5m)
| render timechart
```

#### **Top Errors Tile**
```kusto
// Most common errors
exceptions
| summarize ErrorCount = count() by problemId, outerMessage
| top 10 by ErrorCount desc
```

### **3. Performance Dashboard**

#### **Response Time Tile**
```kusto
// Average response times by operation
requests
| extend Operation = extract(@"operation=([^&]+)", 1, url)
| where isnotempty(Operation)
| summarize 
    AvgDuration = avg(duration),
    P95Duration = percentile(duration, 95)
    by Operation, bin(timestamp, 5m)
| render timechart
```

#### **Email Service Performance**
```kusto
// Email service performance
traces
| where message contains "EMAIL"
| where message contains "SUCCESS" or message contains "BYPASS"
| summarize 
    EmailsSent = countif(message contains "SUCCESS"),
    EmailsBypassed = countif(message contains "BYPASS")
    by bin(timestamp, 1h)
| render columnchart
```

## **Alert Rules Configuration**

### **1. Critical System Alerts**

#### **Function App Down Alert**
```json
{
  "alertName": "Authentication Functions Down",
  "description": "Authentication system is not responding",
  "severity": 0,
  "condition": {
    "query": "requests | where resultCode >= '500' | summarize count() by bin(timestamp, 5m)",
    "threshold": 5,
    "operator": "GreaterThan",
    "timeWindow": "PT5M"
  },
  "actions": [
    {
      "actionType": "email",
      "recipients": ["<EMAIL>", "<EMAIL>"]
    }
  ]
}
```

#### **High Error Rate Alert**
```json
{
  "alertName": "High Authentication Error Rate",
  "description": "Error rate exceeds 10% for authentication operations",
  "severity": 1,
  "condition": {
    "query": "requests | summarize ErrorRate = (countif(resultCode >= '400') * 100.0) / count() | where ErrorRate > 10",
    "threshold": 1,
    "operator": "GreaterThan",
    "timeWindow": "PT10M"
  }
}
```

### **2. Security Alerts**

#### **Unusual Activity Alert**
```json
{
  "alertName": "Unusual Authentication Activity",
  "description": "Spike in authentication requests detected",
  "severity": 2,
  "condition": {
    "query": "requests | where name contains 'AuthenticationService' | summarize count() by bin(timestamp, 5m)",
    "threshold": 100,
    "operator": "GreaterThan",
    "timeWindow": "PT5M"
  }
}
```

#### **Failed Login Attempts Alert**
```json
{
  "alertName": "Multiple Failed Login Attempts",
  "description": "High number of failed login attempts detected",
  "severity": 2,
  "condition": {
    "query": "requests | where url contains 'operation=login' and resultCode >= '400' | summarize count() by bin(timestamp, 5m)",
    "threshold": 20,
    "operator": "GreaterThan",
    "timeWindow": "PT5M"
  }
}
```

### **3. Performance Alerts**

#### **Slow Response Time Alert**
```json
{
  "alertName": "Slow Authentication Response",
  "description": "Authentication operations taking longer than expected",
  "severity": 2,
  "condition": {
    "query": "requests | summarize AvgDuration = avg(duration) | where AvgDuration > 5000",
    "threshold": 1,
    "operator": "GreaterThan",
    "timeWindow": "PT10M"
  }
}
```

## **Log Analysis Queries**

### **1. Operational Queries**

#### **Daily Authentication Summary**
```kusto
requests
| where timestamp > ago(1d)
| extend Operation = extract(@"operation=([^&]+)", 1, url)
| summarize 
    Count = count(),
    SuccessRate = avg(case(resultCode == "200", 1.0, 0.0)) * 100,
    AvgDuration = avg(duration)
    by Operation
| order by Count desc
```

#### **Email Service Status**
```kusto
traces
| where timestamp > ago(1d)
| where message contains "EMAIL"
| extend EmailStatus = case(
    message contains "SUCCESS", "Sent",
    message contains "BYPASS", "Bypassed",
    message contains "ERROR", "Failed",
    "Unknown"
)
| summarize Count = count() by EmailStatus
```

### **2. Security Analysis Queries**

#### **Failed Authentication Attempts**
```kusto
requests
| where timestamp > ago(1d)
| where resultCode >= "400"
| extend Operation = extract(@"operation=([^&]+)", 1, url)
| summarize FailedAttempts = count() by Operation, resultCode
| order by FailedAttempts desc
```

#### **Rate Limiting Events**
```kusto
traces
| where timestamp > ago(1d)
| where message contains "Rate limit exceeded"
| summarize RateLimitHits = count() by bin(timestamp, 1h)
| render timechart
```

## **Automated Health Checks**

### **1. Azure Monitor Health Check**

Create a Logic App or Azure Function to periodically call the UtilityService health endpoint:

```json
{
  "schedule": "0 */5 * * * *",
  "url": "https://your-function-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_KEY",
  "expectedStatus": 200,
  "alertOnFailure": true
}
```

### **2. External Monitoring**

Consider using external monitoring services like:
- Azure Application Insights Availability Tests
- Pingdom
- StatusCake
- UptimeRobot

### **3. Health Check Endpoints**

The system provides these health check endpoints:

```bash
# Overall system health
GET /api/UtilityService?operation=health

# Configuration validation
GET /api/UtilityService?operation=config-check

# System statistics
GET /api/UtilityService?operation=stats

# Graph API connectivity
GET /api/UtilityService?operation=test-graph
```

## **Performance Monitoring**

### **1. Key Performance Indicators (KPIs)**

Monitor these metrics:
- **Response Time**: < 2 seconds for 95% of requests
- **Error Rate**: < 1% for all operations
- **Availability**: > 99.9% uptime
- **Email Delivery**: > 95% success rate

### **2. Performance Queries**

#### **Response Time Percentiles**
```kusto
requests
| where timestamp > ago(1d)
| extend Operation = extract(@"operation=([^&]+)", 1, url)
| summarize 
    P50 = percentile(duration, 50),
    P95 = percentile(duration, 95),
    P99 = percentile(duration, 99)
    by Operation
```

#### **Throughput Analysis**
```kusto
requests
| where timestamp > ago(1d)
| summarize RequestsPerHour = count() by bin(timestamp, 1h)
| render timechart
```

## **Maintenance and Optimization**

### **1. Regular Review Tasks**

**Weekly**:
- Review error logs and trends
- Check performance metrics
- Validate alert configurations

**Monthly**:
- Analyze usage patterns
- Review and update alert thresholds
- Optimize queries and dashboards

**Quarterly**:
- Review monitoring strategy
- Update documentation
- Plan capacity and scaling

### **2. Cost Optimization**

- Configure appropriate data retention policies
- Use sampling for high-volume telemetry
- Archive old logs to cheaper storage
- Review and optimize alert rules

## **Troubleshooting Common Issues**

### **Missing Telemetry Data**
1. Verify Application Insights configuration
2. Check instrumentation key
3. Validate host.json settings
4. Ensure Function App restart after configuration changes

### **Alert Fatigue**
1. Review and adjust alert thresholds
2. Implement alert suppression rules
3. Use severity levels appropriately
4. Group related alerts

### **Performance Issues**
1. Identify slow queries in Application Insights
2. Check for resource constraints
3. Review and optimize code
4. Consider scaling options

This monitoring setup provides comprehensive observability for your authentication system, enabling proactive issue detection and resolution.
