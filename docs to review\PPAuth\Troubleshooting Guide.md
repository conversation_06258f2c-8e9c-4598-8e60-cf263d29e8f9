# Troubleshooting Guide

Quick solutions for common issues with the PowerPages Custom Authentication system.

## 🚨 **Quick Diagnostics**

### **Health Check**

```bash
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_KEY"
```

### **Configuration Check**

```bash
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=config-check&code=YOUR_KEY"
```

## 🔧 **Common Issues**

### **1. "401 Unauthorized" Errors**

**Symptoms**: API calls return 401 status
**Causes**: Missing or incorrect function key

> **⚠️ IMPORTANT**: Each Azure Function has its own unique function key. Using the wrong function key will always result in 401 Unauthorized errors.

**Solutions**:

```bash
# Check if using function key
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_KEY"

# Get function key from Azure Portal:
# Function App → Functions → [SPECIFIC FUNCTION NAME] → Function Keys → Copy default key
# NOTE: Each function (AuthenticationService, PasswordService, InvitationService, UtilityService) has its own key!

# Update Power Pages setting with the CORRECT function-specific key:
# AzureFunctionKey = YOUR_SPECIFIC_FUNCTION_KEY
```

**Function Key Verification**:

- ✅ **Correct**: Use the key from the specific function you're calling
- ❌ **Wrong**: Using a key from a different function in the same Function App

**Verify Fix**:

- Health check should return 200 status
- Power Pages forms should work without 401 errors

### **2. CORS Errors in Browser**

**Symptoms**: Browser console shows CORS policy errors
**Causes**: Domain not in allowedOrigins

**Solutions**:

```json
// Update host.json
{
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": ["https://your-actual-domain.powerappsportals.com"]
      }
    }
  }
}
```

**Steps**:

1. Update host.json with correct domain
2. Redeploy Function App
3. Clear browser cache
4. Test again

### **3. Email Not Sending**

**Symptoms**: Password reset emails not received
**Causes**: SMTP2GO configuration issues

**Diagnostics**:

```bash
# Check email service status
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=config-check&code=YOUR_KEY"
```

**Solutions**:

**API Key Issues**:

```bash
# Verify API key format (should start with "api-")
# Check in Azure Portal → Function App → Configuration
SMTP2GOApiKey = api-your-actual-key
```

**Domain Authentication**:

- Go to SMTP2GO dashboard
- Verify domain authentication is complete
- Check sender email is verified

**Email Bypass Mode**:

```bash
# Check Application Insights logs for:
"EMAIL BYPASS: Password reset email would be sent to..."
# This means API key is not configured
```

### **4. User Registration Fails**

**Symptoms**: Registration form shows errors
**Causes**: Various validation or configuration issues

**Diagnostics**:

```javascript
// Check browser console for errors
// Look for specific error messages
```

**Common Solutions**:

**Password History Error**:

```
"Password has been used recently"
```

- User trying to reuse a password
- Normal behavior - ask user to choose different password

**Entra External ID Errors**:

```
"User creation failed"
```

- Check Entra External ID configuration
- Verify client secret is correct
- Check API permissions

**Validation Errors**:

```
"Invalid email format"
"Password must be at least 8 characters"
```

- Client-side validation working correctly
- User needs to fix input

### **5. Password Reset Not Working**

**Symptoms**: Reset process fails at various steps

**Step-by-Step Diagnosis**:

**Step 1 - Email Sending**:

```bash
# Test reset initiation
curl -X POST "https://your-app.azurewebsites.net/api/PasswordService?operation=reset-initiate&code=YOUR_KEY" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","applicationName":"Test App"}'
```

**Step 2 - Token Validation**:

- Check if user received email
- Verify 6-digit code format
- Check token hasn't expired (30 minutes)

**Step 3 - Password Update**:

```bash
# Check Application Insights for detailed errors
# Look for Graph API errors or password validation failures
```

### **6. Function App Not Responding**

**Symptoms**: All endpoints return errors or timeouts

**Quick Checks**:

```bash
# Check if Function App is running
az functionapp show --name your-app --resource-group your-rg --query state

# Restart if needed
az functionapp restart --name your-app --resource-group your-rg
```

**Common Causes**:

- Function App stopped
- Configuration errors preventing startup
- Resource limits exceeded

### **7. Power Pages Integration Issues**

**Symptoms**: Custom pages not working correctly

**JavaScript Errors**:

```javascript
// Check browser console for:
"Azure Function URL not configured";
"CORS policy error";
"Failed to fetch";
```

**Configuration Check**:

```
// Verify Power Pages settings:
AzureFunctionUrl = https://your-app.azurewebsites.net/api
AzureFunctionKey = your-function-key (if using Function authorization)
ApplicationName = Your App Name
```

**File Upload Issues**:

- Ensure all JavaScript files are uploaded to Power Pages
- Check file paths are correct
- Verify CSS file is linked properly

## 🔍 **Debugging Tools**

### **Application Insights Queries**

**Recent Errors**:

```kusto
exceptions
| where timestamp > ago(1h)
| top 10 by timestamp desc
| project timestamp, problemId, outerMessage
```

**Failed Requests**:

```kusto
requests
| where timestamp > ago(1h) and resultCode >= "400"
| project timestamp, name, url, resultCode, duration
| order by timestamp desc
```

**Email Service Logs**:

```kusto
traces
| where timestamp > ago(1h) and message contains "EMAIL"
| project timestamp, message
| order by timestamp desc
```

**Rate Limiting Events**:

```kusto
traces
| where message contains "Rate limit exceeded"
| summarize count() by bin(timestamp, 5m)
| render timechart
```

### **Function App Logs**

**Live Log Stream**:

```bash
# Via Azure CLI
az webapp log tail --name your-function-app --resource-group your-rg

# Via Azure Portal
# Function App → Monitoring → Log stream
```

### **Power Pages Debugging**

**Browser Console**:

- Open Developer Tools (F12)
- Check Console tab for JavaScript errors
- Look for network errors in Network tab

**Common JavaScript Issues**:

```javascript
// Missing configuration
"ERROR_MISSING_AzureFunctionUrl";

// CORS errors
"Access to fetch at '...' from origin '...' has been blocked by CORS policy";

// Function key errors
"401 Unauthorized";
```

## 🛠️ **Performance Issues**

### **Slow Response Times**

**Diagnostics**:

```kusto
requests
| where timestamp > ago(1h)
| summarize avg(duration), percentile(duration, 95) by name
| order by avg_duration desc
```

**Common Causes**:

- Cold start delays (first request after idle)
- Database/storage latency
- External API calls (Graph API, SMTP2GO)

**Solutions**:

- Consider Premium plan for always-on
- Optimize database queries
- Implement caching where appropriate

### **High Memory Usage**

**Symptoms**: Function timeouts or out-of-memory errors

**Solutions**:

- Check for memory leaks in code
- Optimize object creation
- Consider scaling up Function App plan

## 🚨 **Emergency Procedures**

### **System Down**

**Immediate Actions**:

1. Check Function App status
2. Restart Function App if needed
3. Verify configuration hasn't changed
4. Check for Azure service outages

**Communication**:

- Notify users of authentication issues
- Provide estimated resolution time
- Update status page if available

### **Security Incident**

**Immediate Actions**:

```bash
# Stop Function App
az functionapp stop --name your-app --resource-group your-rg

# Rotate secrets
az ad app credential reset --id YOUR_APP_ID

# Update Key Vault
az keyvault secret set --vault-name your-vault --name ClientSecret --value NEW_SECRET
```

### **Data Issues**

**Password History Corruption**:

- Check blob storage connectivity
- Verify storage account access keys
- Review recent changes to storage configuration

## 📞 **Getting Help**

### **Information to Gather**

Before seeking help, collect:

- Correlation ID from error response
- Timestamp of issue
- Exact error message
- Steps to reproduce
- Browser/environment details

### **Log Analysis**

**Find Correlation ID**:

```kusto
requests
| where customDimensions.CorrelationId == "your-correlation-id"
| union (traces | where customDimensions.CorrelationId == "your-correlation-id")
| order by timestamp asc
```

### **Health Check Results**

Always include current health check output:

```bash
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_KEY"
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=config-check&code=YOUR_KEY"
```

## ✅ **Prevention**

### **Regular Monitoring**

- Set up Application Insights alerts
- Monitor health check endpoints
- Review error logs weekly
- Check performance metrics

### **Configuration Management**

- Document all configuration changes
- Test changes in staging first
- Keep backup of working configurations
- Use Infrastructure as Code when possible

### **Maintenance Schedule**

**Weekly**:

- Review error logs
- Check performance metrics
- Verify backup integrity

**Monthly**:

- Rotate access keys
- Review security configurations
- Update documentation

**Quarterly**:

- Full system health review
- Security audit
- Performance optimization
