.registration-container {
    max-width: 650px;
}

.registration-container h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background-color: var(--primary-red);
    margin: 1rem auto;
}

#registerButton {
    background-color: var(--primary-red);
    color: var(--color-white);
    border: 2px solid var(--primary-red);
}

#registerButton:hover {
    background-color: var(--primary-red-dark);
    border-color: var(--primary-red-dark);
}

#registrationForm .input-group .form-control {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#registrationForm .input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

#registrationForm .input-group .btn:hover {
    background-color: var(--primary-red);
    border-color: var(--primary-red);
    color: var(--color-white);
}

#registrationForm .form-text.text-success {
    color: #28a745 !important;
}

#registrationForm .form-check-label a:hover {
    color: var(--primary-red-dark);
    text-decoration: underline;
}

#submitButton {
    margin-top: 1rem;
}

#messageContent {
    padding: 1rem;
    border-radius: var(--radius-md);
    border-left: 4px solid;
}

.text-center.mt-3 {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-border);
}

@media (max-width: 768px) {
    .registration-container {
        max-width: 95%;
    }
}
