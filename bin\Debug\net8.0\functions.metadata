[{"name": "InvitationService", "scriptFile": "PowerPagesCustomAuth.dll", "entryPoint": "PasswordHistoryValidator.InvitationFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "req", "direction": "In", "type": "httpTrigger", "authLevel": "Function", "methods": ["post", "options"], "properties": {}}, {"name": "$return", "type": "http", "direction": "Out"}]}, {"name": "PasswordService", "scriptFile": "PowerPagesCustomAuth.dll", "entryPoint": "PasswordHistoryValidator.PasswordFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "req", "direction": "In", "type": "httpTrigger", "authLevel": "Function", "methods": ["post", "options"], "properties": {}}, {"name": "$return", "type": "http", "direction": "Out"}]}, {"name": "RegistrationService", "scriptFile": "PowerPagesCustomAuth.dll", "entryPoint": "PasswordHistoryValidator.RegistrationFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "req", "direction": "In", "type": "httpTrigger", "authLevel": "Function", "methods": ["post", "options"], "properties": {}}, {"name": "$return", "type": "http", "direction": "Out"}]}, {"name": "UtilityService", "scriptFile": "PowerPagesCustomAuth.dll", "entryPoint": "PasswordHistoryValidator.UtilityFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "req", "direction": "In", "type": "httpTrigger", "authLevel": "Function", "methods": ["post", "options"], "properties": {}}, {"name": "$return", "type": "http", "direction": "Out"}]}]