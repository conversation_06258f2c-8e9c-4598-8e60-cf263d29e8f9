# Security Enhancements for July 8th

**Date**: July 8th, 2025
**Purpose**: Implement critical security improvements by changing authorization levels and restricting CORS
**Status**: ⚠️ **REQUIRES COMPLETION** - Power Pages integration missing

## 🚨 **CRITICAL WARNING**

**DO NOT DEPLOY** the current Azure Functions to production without completing ALL steps below. The functions are currently configured with `AuthorizationLevel.Function` but the Power Pages JavaScript files do not include function keys, which will cause **ALL API calls to fail with 401 Unauthorized errors**.

**Current Issue**: Registration and password reset forms will be completely broken until Power Pages JavaScript files are updated to include function keys.

## 🎯 **Current Status**

### **Code Changes Completed** ✅

1. **AuthenticationService.cs**: Changed from `AuthorizationLevel.Anonymous` to `AuthorizationLevel.Function`
2. **PasswordService.cs**: Changed from `AuthorizationLevel.Anonymous` to `AuthorizationLevel.Function`
3. **UtilityService.cs**: Changed from `AuthorizationLevel.Anonymous` to `AuthorizationLevel.Function`

### **Still Required** ❌

1. **Power Pages JavaScript**: Must be updated to include function keys in API calls
2. **host.json**: CORS domain needs to be updated from placeholder
3. **Power Pages Settings**: Function key configuration needed

## 📋 **Implementation Steps**

### **STEP 1: Find Your Power Pages Domain** ⚠️ **CRITICAL FIRST**

Before proceeding, you MUST identify your exact Power Pages domain:

1. **Go to Power Pages Admin Portal**
2. **Navigate to**: Setup → Domain Management (or look at your browser URL)
3. **Copy the exact domain** (example: `https://site-dccs0.powerappsportals.com`)
4. **Write it down**: **\*\***\*\***\*\***\_\_\_\_**\*\***\*\***\*\***

### **STEP 2: Update CORS Configuration**

1. **Open**: `host.json` file in your project
2. **Find the line**: `"allowedOrigins": ["https://your-actual-domain.powerappsportals.com"]`
3. **Replace** `your-actual-domain.powerappsportals.com` with your actual domain from Step 1
4. **Save the file**

**Example**:

```json
"allowedOrigins": ["https://site-dccs0.powerappsportals.com"]
```

### **STEP 3: Update Power Pages JavaScript Files** ⚠️ **CRITICAL**

**IMPORTANT**: The current Power Pages JavaScript files do NOT include function key support. They must be updated before deployment.

#### **3.1: Update registration.js**

1. **Open**: `power-pages-files\registration.js`
2. **Find the line** (around line 218):
   ```javascript
   const response = await fetch(`${AZURE_FUNCTION_URL}?operation=register`, {
   ```
3. **Replace with**:
   ```javascript
   const functionKey = window.appConfig?.azureFunctionKey ||
                      document.querySelector('meta[name="azure-function-key"]')?.content ||
                      null;
   if (!functionKey) {
     throw new Error('Azure Function key not configured');
   }
   const response = await fetch(`${AZURE_FUNCTION_URL}?operation=register&code=${functionKey}`, {
   ```

#### **3.2: Update forgot-password.js**

1. **Open**: `power-pages-files\forgot-password.js`
2. **Find all fetch calls** to PASSWORD_SERVICE_URL
3. **Add** `&code=${functionKey}` to each URL
4. **Add function key retrieval** similar to registration.js

#### **3.3: Update registration.html**

1. **Open**: `power-pages-files\registration.html`
2. **Find the appConfig section** (around line 7)
3. **Add**:
   ```javascript
   azureFunctionKey: {{ settings['AzureFunctionKey'] | default: 'ERROR_MISSING_AzureFunctionKey' | json }},
   ```

### **STEP 4: Build and Test Locally** (Optional but Recommended)

1. **Open terminal** in your project directory
2. **Run**:
   ```bash
   dotnet clean
   dotnet build
   ```
3. **Check for errors** - fix any compilation issues
4. **Test locally** (optional):
   ```bash
   func start
   ```

### **STEP 5: Deploy to Azure**

1. **Open VS Code** with your project
2. **Press**: `Ctrl+Shift+P` (Windows) or `Cmd+Shift+P` (Mac)
3. **Type**: "Azure Functions: Deploy to Function App"
4. **Select**: Your production Function App
5. **Confirm deployment**
6. **Wait for completion** (watch the output window)

### **STEP 6: Get Function Keys** ⚠️ **CRITICAL**

After deployment completes:

1. **Go to**: Azure Portal (portal.azure.com)
2. **Navigate to**: Your Function App
3. **Go to**: Functions → AuthenticationService (or any function)
4. **Click**: Function Keys
5. **Copy the default key** (starts with something like `FtLd7oJsiUSGOKlM...`)
6. **Write it down securely**: **\*\***\*\***\*\***\_\_\_\_**\*\***\*\***\*\***

### **STEP 7: Update Power Pages Configuration**

1. **Go to**: Power Pages Admin Portal
2. **Navigate to**: Setup → Site Settings
3. **Add new setting**:
   - **Name**: `AzureFunctionKey`
   - **Value**: [paste the function key from Step 6]
4. **Save the setting**

### **STEP 8: Test the Security Changes**

#### **Test 1: Verify Function Authorization**

Open a new browser tab and try:

```
https://your-function-app.azurewebsites.net/api/UtilityService?operation=health
```

**Expected Result**: Should return `401 Unauthorized` (this is good!)

#### **Test 2: Verify Function Key Works**

Try with the function key:

```
https://your-function-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_FUNCTION_KEY
```

**Expected Result**: Should return health status (this is good!)

#### **Test 3: Test Power Pages Forms**

1. **Go to**: Your Power Pages registration form
2. **Try registering** a test user
3. **Expected Result**: Should work normally

#### **Test 4: Test Password Reset**

1. **Go to**: Your forgot password form
2. **Try initiating** a password reset
3. **Expected Result**: Should work normally

### **STEP 9: Verify CORS Restrictions** (Advanced)

1. **Open browser developer tools** (F12)
2. **Go to Console tab**
3. **Try accessing from unauthorized domain**:
   ```javascript
   fetch(
     "https://your-function-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_KEY"
   );
   ```
4. **Expected Result**: Should be blocked by CORS policy

## 🚨 **Troubleshooting**

### **Issue: Power Pages Forms Not Working**

**Symptoms**: Registration/password reset forms show errors

**Solutions**:

1. **Check Function Key**: Verify `AzureFunctionKey` setting in Power Pages
2. **Check Domain**: Verify CORS domain matches exactly
3. **Check Deployment**: Ensure Function App deployed successfully

### **Issue: 401 Unauthorized Errors**

**Symptoms**: All API calls return 401

**Solutions**:

1. **Verify Function Key**: Check the key is correct and complete
2. **Check Power Pages Setting**: Ensure `AzureFunctionKey` is set
3. **Restart Function App**: Sometimes needed after deployment

### **Issue: CORS Errors in Browser**

**Symptoms**: Browser console shows CORS policy errors

**Solutions**:

1. **Check Domain Spelling**: Verify exact domain in host.json
2. **Include https://**: Ensure protocol is included
3. **Redeploy**: CORS changes require redeployment

## ✅ **Verification Checklist**

After completing all steps:

- [ ] **Code Changes**: All three functions changed to `AuthorizationLevel.Function`
- [ ] **CORS Updated**: host.json contains your actual Power Pages domain
- [ ] **Deployed**: Function App successfully deployed
- [ ] **Function Key**: Retrieved and documented securely
- [ ] **Power Pages**: `AzureFunctionKey` setting added
- [ ] **Health Check**: Works with function key, fails without
- [ ] **Registration**: Power Pages registration form works
- [ ] **Password Reset**: Power Pages password reset works
- [ ] **CORS**: Unauthorized domains are blocked

## 📊 **Security Improvement Summary**

### **Before (Testing Configuration)**

- ❌ **Authorization**: Anonymous (anyone can call APIs)
- ❌ **CORS**: Allows all origins (any website can call APIs)
- ⚠️ **Risk Level**: HIGH

### **After (Production Configuration)**

- ✅ **Authorization**: Function keys required (authenticated access only)
- ✅ **CORS**: Restricted to Power Pages domain only
- ✅ **Risk Level**: LOW

## 🔐 **Security Benefits Achieved**

1. **API Protection**: All endpoints now require authentication
2. **Origin Restriction**: Only your Power Pages site can call the APIs
3. **Key Management**: Centralized function key management in Azure
4. **Audit Trail**: All API calls now logged with authentication context

## 📞 **Support Information**

### **If You Need Help**

1. **Check Application Insights**: Look for detailed error logs
2. **Use Health Check**: `UtilityService?operation=health&code=YOUR_KEY`
3. **Review This Document**: Follow troubleshooting steps
4. **Test Step by Step**: Don't skip verification steps

### **Emergency Rollback** (If Needed)

If something goes wrong and you need to quickly restore access:

1. **Change authorization back to Anonymous** in the three function files
2. **Change CORS back to `["*"]`** in host.json
3. **Redeploy immediately**
4. **Remove `AzureFunctionKey`** from Power Pages settings

**Note**: Only use emergency rollback if absolutely necessary - it removes security protections.

## 🎉 **Completion**

Once all steps are completed and verified, your PowerPages Custom Authentication system will have significantly improved security with:

- **Function-level authorization** protecting all APIs
- **Domain-restricted CORS** preventing unauthorized access
- **Maintained functionality** for all legitimate users

**Congratulations on implementing these critical security enhancements!**
