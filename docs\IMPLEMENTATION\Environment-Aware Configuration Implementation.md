# Environment-Aware Configuration Implementation

## Overview

This document provides the complete implementation of environment-aware configuration validation for Azure Functions, addressing the Key Vault strategy analysis recommendations.

## Implementation Summary

### **Files Created/Modified**

1. **`Shared/EnvironmentDetector.cs`** - NEW: Environment detection logic
2. **`Shared/ConfigurationValidator.cs`** - MODIFIED: Environment-aware validation
3. **`docs/RECOMMENDATIONS/Azure Functions Key Vault Strategy Analysis.md`** - NEW: Strategy analysis

### **Key Features Implemented**

✅ **Environment Detection**: Automatically detects local vs. Azure vs. production environments
✅ **Environment-Specific Validation**: Different validation rules for different environments
✅ **Security-First Production**: Key Vault required for sensitive data in production
✅ **Development Flexibility**: Direct values acceptable in local development
✅ **Clear Error Messages**: Environment-specific guidance for configuration issues

## Configuration Examples

### **Local Development (Recommended)**

**File: `local.settings.json`**
```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    
    "EntraExternalID:TenantId": "your-dev-tenant-id",
    "EntraExternalID:ClientId": "your-dev-client-id",
    "EntraExternalID:ClientSecret": "your-dev-client-secret",
    "EntraExternalID:DefaultDomain": "yourdevtenant.onmicrosoft.com",
    
    "SendGrid:ApiKey": "SG.your-dev-api-key",
    "SendGrid:FromEmail": "<EMAIL>",
    "SendGrid:PasswordResetTemplateId": "d-your-dev-template-id",
    "SendGrid:PasswordChangedTemplateId": "d-your-dev-template-id",
    "SendGrid:UserInvitationTemplateId": "d-your-dev-template-id",
    "SendGrid:AccountCreatedTemplateId": "d-your-dev-template-id",
    "SendGrid:PasswordExpirationTemplateId": "d-your-dev-template-id",
    "SendGrid:PasswordExpiredTemplateId": "d-your-dev-template-id",
    
    "PasswordReset:BaseUrl": "https://localhost:7071/reset-password",
    "AccountRegistration:BaseUrl": "https://localhost:7071/registration"
  }
}
```

### **Local Development with Key Vault (Optional)**

**File: `local.settings.json`**
```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    
    "KeyVaultUrl": "https://carthos-dev-vault.vault.azure.net/",
    
    "EntraExternalID:TenantId": "your-dev-tenant-id",
    "EntraExternalID:ClientId": "your-dev-client-id",
    "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://carthos-dev-vault.vault.azure.net/secrets/DevEntraClientSecret/)",
    "EntraExternalID:DefaultDomain": "yourdevtenant.onmicrosoft.com",
    
    "SendGrid:ApiKey": "@Microsoft.KeyVault(SecretUri=https://carthos-dev-vault.vault.azure.net/secrets/DevSendGridApiKey/)",
    "SendGrid:FromEmail": "<EMAIL>"
  }
}
```

### **Production (Azure Function App Settings)**

**Key Vault Secrets:**
```bash
# Store in Azure Key Vault
az keyvault secret set --vault-name carthos-auth-vault --name "EntraClientSecret" --value "your-production-secret"
az keyvault secret set --vault-name carthos-auth-vault --name "SendGridApiKey" --value "your-production-api-key"
```

**Function App Settings:**
```json
{
  "KeyVaultUrl": "https://carthos-auth-vault.vault.azure.net/",
  
  "EntraExternalID:TenantId": "your-production-tenant-id",
  "EntraExternalID:ClientId": "your-production-client-id",
  "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://carthos-auth-vault.vault.azure.net/secrets/EntraClientSecret/)",
  "EntraExternalID:DefaultDomain": "sfrb3.onmicrosoft.com",
  
  "SendGrid:ApiKey": "@Microsoft.KeyVault(SecretUri=https://carthos-auth-vault.vault.azure.net/secrets/SendGridApiKey/)",
  "SendGrid:FromEmail": "<EMAIL>",
  "SendGrid:PasswordResetTemplateId": "d-production-template-id",
  "SendGrid:PasswordChangedTemplateId": "d-production-template-id",
  "SendGrid:UserInvitationTemplateId": "d-production-template-id",
  "SendGrid:AccountCreatedTemplateId": "d-production-template-id",
  "SendGrid:PasswordExpirationTemplateId": "d-production-template-id",
  "SendGrid:PasswordExpiredTemplateId": "d-production-template-id",
  
  "PasswordReset:BaseUrl": "https://auth.osler.com/reset-password",
  "AccountRegistration:BaseUrl": "https://auth.osler.com/registration"
}
```

## Environment Detection Logic

### **How Environments Are Detected**

```csharp
// Local Development
WEBSITE_SITE_NAME = null (not set)
AZURE_FUNCTIONS_ENVIRONMENT = "Development" (or not set)

// Azure Development/Staging
WEBSITE_SITE_NAME = "your-function-app-name"
AZURE_FUNCTIONS_ENVIRONMENT = "Development" or "Staging"

// Azure Production
WEBSITE_SITE_NAME = "your-function-app-name"
AZURE_FUNCTIONS_ENVIRONMENT = "Production"
```

### **Validation Rules by Environment**

| Environment | Key Vault Required | Sensitive Data Rules | Error Messages |
|-------------|-------------------|---------------------|----------------|
| **Local Development** | Optional | Direct values OK | Development-friendly guidance |
| **Azure Development** | Required if configured | Key Vault preferred | Environment-specific warnings |
| **Azure Production** | Required | Must use Key Vault | Security-focused requirements |

## Error Message Examples

### **Local Development Errors**

```
Configuration validation failed for Local Development environment:

• EntraExternalID:ClientSecret is required. Set your Azure AD B2C application client secret or use Key Vault reference.
• SendGrid:ApiKey is required. Set your SendGrid API key or use Key Vault reference.

Please configure these values in your application settings, local.settings.json, or Azure Key Vault.
```

### **Production Environment Errors**

```
Configuration validation failed for Production (Azure) environment:

• KeyVaultUrl is required in production environment for secure secret storage.
• EntraExternalID:ClientSecret must use Key Vault reference in production environment (@Microsoft.KeyVault).
• SendGrid:ApiKey must use Key Vault reference in production environment (@Microsoft.KeyVault).

Please configure these values in your application settings, local.settings.json, or Azure Key Vault.
```

## Migration Steps

### **Step 1: Fix Current Local Configuration**

Update your `local.settings.json` to remove placeholder values:

```json
{
  "Values": {
    "KeyVaultUrl": "",  // Remove or set to empty to disable Key Vault locally
    "EntraExternalID:ClientSecret": "your-actual-dev-secret",
    "SendGrid:ApiKey": "SG.your-actual-dev-key"
  }
}
```

### **Step 2: Test Local Development**

```bash
# Should start successfully now
func start
```

### **Step 3: Set Up Production Key Vault**

```bash
# Create Key Vault (if not exists)
az keyvault create --name carthos-auth-vault --resource-group carthos-azure-sandbox --location eastus

# Store production secrets
az keyvault secret set --vault-name carthos-auth-vault --name "EntraClientSecret" --value "your-production-secret"
az keyvault secret set --vault-name carthos-auth-vault --name "SendGridApiKey" --value "your-production-api-key"

# Configure Function App managed identity
az functionapp identity assign --name your-function-app --resource-group carthos-azure-sandbox

# Grant Key Vault access
az keyvault set-policy --name carthos-auth-vault --object-id [function-app-identity] --secret-permissions get
```

### **Step 4: Update Production Configuration**

In Azure Portal → Function App → Configuration:
- Set `KeyVaultUrl` to your actual Key Vault URL
- Set sensitive settings to use Key Vault references
- Set non-sensitive settings as direct values

## Benefits Achieved

### **Security Benefits**
✅ **Production secrets secured** in Key Vault with RBAC
✅ **Environment-appropriate security** - no forced Key Vault in development
✅ **Clear security boundaries** between sensitive and non-sensitive configuration
✅ **Audit trail** through Key Vault access logging

### **Development Benefits**
✅ **Faster local setup** - no Key Vault dependency for basic development
✅ **Flexible testing** - can test Key Vault integration when needed
✅ **Clear error messages** - environment-specific guidance
✅ **Environment parity** - production configuration structure mirrors local

### **Operational Benefits**
✅ **Simplified deployment** - clear separation of secrets and configuration
✅ **Better monitoring** - environment-aware logging and validation
✅ **Easier secret rotation** - centralized in Key Vault for production
✅ **Compliance ready** - meets enterprise security requirements

## Testing the Implementation

### **Test Local Development**
```bash
# Remove Key Vault URL to test direct values
"KeyVaultUrl": "",

# Start application
func start

# Should start successfully with direct configuration values
```

### **Test Production Validation**
```bash
# Set environment to production
"AZURE_FUNCTIONS_ENVIRONMENT": "Production",

# Try with direct values (should fail)
"EntraExternalID:ClientSecret": "direct-value",

# Should get error requiring Key Vault references
```

## Conclusion

This implementation provides:
- **Environment-aware configuration validation**
- **Security-first production approach**
- **Development-friendly local setup**
- **Clear error messages and guidance**
- **Flexible Key Vault integration**

The solution balances security requirements with development efficiency, ensuring production secrets are properly secured while maintaining a smooth development experience.
