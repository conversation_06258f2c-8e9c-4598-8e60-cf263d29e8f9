# Disabling Self-Service Sign-Up in Entra External ID

Disable the "No account? Create one" link in the standard Entra External ID login interface to prevent users from bypassing the custom password history validation system.

Configure the Entra External ID user flow to disable self-service sign-up. This is a **one-time configuration change** that affects all applications using the user flow.

## Method 1: Azure Portal UI (Recommended)

The simplest approach is through the Azure Portal:

1. Go to Azure Portal (portal.azure.com)
2. Navigate to Microsoft Entra ID > External Identities > User flows
3. Select your user flow
4. Look for "Properties" in the left menu
5. Find the option "Enable self-service sign up" and disable it

## Method 2: Microsoft Graph API

There are two API endpoints that can be used to disable sign-up, depending on your tenant configuration:

### Option A: New Authentication Events Flow API

```http
PATCH https://graph.microsoft.com/beta/identity/authenticationEventsFlows/{user-flow-id}
```

With this request body:
```json
{
    "@odata.type": "#microsoft.graph.externalUsersSelfServiceSignUpEventsFlow",
    "onInteractiveAuthFlowStart": {
        "@odata.type": "#microsoft.graph.onInteractiveAuthFlowStartExternalUsersSelfServiceSignUp",
        "isSignUpAllowed": false
    }
}
```

**Note:** This API requires the `EnableMsGraphAuthenticationEventListener` feature flag to be enabled on your tenant. If you receive an error about this feature flag, use Option B or contact Microsoft Support to enable this feature for your tenant.

### Option B: Legacy B2X User Flow API

```http
PATCH https://graph.microsoft.com/beta/identity/b2xUserFlows/{user-flow-id}
```

With this request body:
```json
{
  "isSignUpAllowed": false
}
```

### Finding Your User Flow ID

1. **List all user flows**:
   ```http
   GET https://graph.microsoft.com/beta/identity/b2xUserFlows
   ```

2. **Find user flow by application ID**:
   ```http
   GET https://graph.microsoft.com/beta/identity/authenticationEventsFlows?$filter=applicationIds/any(id:id eq '{application-id}')
   ```
   Replace `{application-id}` with your application's client ID

### Required Permissions

- `IdentityUserFlow.ReadWrite.All` (Application or Delegated)

## Method 3: PowerShell with Azure AD Module

```powershell
# Install module if needed
Install-Module AzureADPreview -AllowClobber

# Connect to your tenant
Connect-AzureAD -TenantId "your-tenant-id"

# Get your user flow
$userFlow = Get-AzureADMSB2CUserFlow -Id "B2C_1_signupsignin"

# Update the user flow to disable sign-up
Set-AzureADMSB2CUserFlow -Id $userFlow.Id -IsSignUpAllowed $false
```

## Troubleshooting

### Feature Flag Error

If you receive this error:
```json
{
    "error": {
        "code": "AADB2C",
        "message": "Unauthorized. Access to this Api requires feature: 'EnableMsGraphAuthenticationEventListener' for the tenant: '{tenant-id}'."
    }
}
```

**Solutions:**
1. Use the legacy B2X User Flow API (Method 2, Option B)
2. Use the Azure Portal UI (Method 1)
3. Open a support ticket with Microsoft to enable the `EnableMsGraphAuthenticationEventListener` feature for your tenant

[Configure a user flow for sign-up and sign-in](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-user-flow-sign-up-sign-in-customers)

## Configuration

### Test the Login Interface

1. Navigate to your Power Pages login page
2. Click "Sign In with Microsoft"
3. You should be redirected to the Entra External ID login page
4. **Verify**: The "No account? Create one" link should **not be visible**
5. **Verify**: Only the login form should be available

### Test Direct Access

1. Try to access the sign-up URL directly:
   ```
   https://[your-tenant].ciamlogin.com/[tenant-id]/oauth2/v2.0/authorize?p=B2C_1_signupsignin&response_type=code&scope=openid&client_id=[client-id]&redirect_uri=[redirect-uri]&response_mode=form_post&signup=true
   ```
2. **Expected Result**: Should redirect to login page or show an error
3. **Should NOT**: Show a registration form

### Test Custom Registration

1. Navigate to your custom registration page (`/Custom-User-Registration`)
2. Fill out the registration form
3. **Verify**: Registration still works through your custom process
4. **Verify**: Password history validation is applied

## Configuration Examples

### Example 1: Azure Portal Configuration

If configuring through Azure Portal:

```json
{
  "userFlowType": "signUpOrSignIn",
  "userFlowTypeVersion": "v2",
  "isSignUpAllowed": false,
  "isPasswordResetAllowed": true,
  "identityProviders": [
    {
      "type": "EmailPassword"
    }
  ]
}
```

### Example 2: Microsoft Graph API Configuration

If configuring via API:

```http
PATCH https://graph.microsoft.com/beta/identity/b2cUserFlows/{userFlowId}
Content-Type: application/json

{
  "isSignUpAllowed": false
}
```

### Example 3: PowerShell Configuration

If using PowerShell:

```powershell
# Connect to Microsoft Graph
Connect-MgGraph -Scopes "IdentityUserFlow.ReadWrite.All"

# Update user flow
Update-MgIdentityB2CUserFlow -B2CIdentityUserFlowId "B2C_1_signupsignin" -IsSignUpAllowed:$false
```

## Alternative Approaches to Investigate

### Custom Domain with Branded Experience

1. **Configure custom domain** for Entra External ID
2. **Customize login page** branding and layout
3. **Remove sign-up elements** through custom CSS/JavaScript
4. **Redirect sign-up attempts** to your custom registration page

### Conditional Access Policies

1. **Create conditional access policy** that blocks sign-up for certain users
2. **Use group-based access** to control who can register
3. **Implement approval workflows** for new user registration

### API-Only Registration

1. **Disable all user flows** for registration
2. **Use Microsoft Graph API** exclusively for user creation
3. **Implement custom approval process** before creating users
4. **Full control** over user creation and validation

## Microsoft Documentation References

### Entra External ID Security

- [Microsoft Entra External ID security overview](https://learn.microsoft.com/en-us/entra/external-id/external-identities-overview)
- [Configure user flows for customers](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-user-flow-sign-up-sign-in-customers)
- [Disable self-service sign-up](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-enable-self-service-sign-up)
- [Custom branding for Entra External ID](https://learn.microsoft.com/en-us/entra/external-id/customers/how-to-customize-branding-customers)

### Authentication Security Best Practices

- [Microsoft identity platform security best practices](https://learn.microsoft.com/en-us/entra/identity-platform/security-best-practices-for-app-registration)
- [OAuth 2.0 security considerations](https://learn.microsoft.com/en-us/entra/identity-platform/v2-oauth2-auth-code-flow#security-considerations)
- [OpenID Connect security](https://learn.microsoft.com/en-us/entra/identity-platform/v2-protocols-oidc)
- [Token validation](https://learn.microsoft.com/en-us/entra/identity-platform/access-tokens#validating-tokens)

### Conditional Access and Policies

- [Conditional Access overview](https://learn.microsoft.com/en-us/entra/identity/conditional-access/overview)
- [Conditional Access for External ID](https://learn.microsoft.com/en-us/entra/external-id/conditional-access)
- [Identity Protection](https://learn.microsoft.com/en-us/entra/id-protection/overview-identity-protection)
- [Risk-based access policies](https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-policies)

### Azure Security Features

- [Azure security baseline](https://learn.microsoft.com/en-us/security/benchmark/azure/baselines/azure-functions-security-baseline)
- [Azure Key Vault security](https://learn.microsoft.com/en-us/azure/key-vault/general/security-features)
- [Managed identities security](https://learn.microsoft.com/en-us/entra/identity/managed-identities-azure-resources/managed-identities-faq)
- [Azure Storage security](https://learn.microsoft.com/en-us/azure/storage/common/storage-security-guide)

### Power Pages Security

- [Power Pages security overview](https://learn.microsoft.com/en-us/power-pages/security/power-pages-security)
- [Authentication and authorization](https://learn.microsoft.com/en-us/power-pages/security/authentication/authentication-overview)
- [Web application firewall](https://learn.microsoft.com/en-us/power-pages/security/web-application-firewall)
- [Content Security Policy](https://learn.microsoft.com/en-us/power-pages/security/content-security-policy)

### Microsoft Graph API Security

- [Microsoft Graph security guidance](https://learn.microsoft.com/en-us/graph/security-authorization)
- [Application permissions vs delegated permissions](https://learn.microsoft.com/en-us/graph/auth/auth-concepts#microsoft-graph-permissions)
- [Throttling and rate limiting](https://learn.microsoft.com/en-us/graph/throttling)
- [Error handling](https://learn.microsoft.com/en-us/graph/errors)

### Compliance and Governance

- [Microsoft compliance offerings](https://learn.microsoft.com/en-us/compliance/regulatory/offering-home)
- [Azure Policy](https://learn.microsoft.com/en-us/azure/governance/policy/overview)
- [Azure Blueprints](https://learn.microsoft.com/en-us/azure/governance/blueprints/overview)
- [Microsoft Purview](https://learn.microsoft.com/en-us/purview/purview)
