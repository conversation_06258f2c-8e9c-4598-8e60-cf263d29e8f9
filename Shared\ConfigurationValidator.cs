using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Provides centralized configuration validation with fail-fast behavior.
/// All required configuration values are validated at startup with clear error messages.
/// </summary>
public static class ConfigurationValidator
{
    /// <summary>
    /// Validates all required configuration values and throws InvalidOperationException
    /// with clear error messages if any are missing or invalid.
    /// Simple approach: Local development allows direct values, Azure production requires Key Vault for sensitive data.
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <exception cref="InvalidOperationException">Thrown when required configuration is missing or invalid</exception>
    public static void ValidateRequiredConfiguration(IConfiguration configuration)
    {
        var errors = new List<string>();
        var isAzureHosted = !string.IsNullOrEmpty(configuration["WEBSITE_SITE_NAME"]);

        // Validate all required configuration
        ValidateEntraConfiguration(configuration, errors, isAzureHosted);
        ValidateStorageConfiguration(configuration, errors);
        ValidateSendGridConfiguration(configuration, errors, isAzureHosted);
        ValidateUrlConfiguration(configuration, errors);
        ValidateKeyVaultConfiguration(configuration, errors);

        if (errors.Count > 0)
        {
            var environment = isAzureHosted ? "Azure" : "Local Development";
            var errorMessage = $"Configuration validation failed for {environment} environment:\n\n" +
                              string.Join("\n", errors.Select(e => $"• {e}")) +
                              "\n\nPlease configure these values in your application settings, local.settings.json, or Azure Key Vault.";

            throw new InvalidOperationException(errorMessage);
        }
    }

    private static void ValidateEntraConfiguration(IConfiguration configuration, List<string> errors, bool isAzureHosted)
    {
        var tenantId = configuration["EntraExternalID:TenantId"];
        var clientId = configuration["EntraExternalID:ClientId"];
        var clientSecret = configuration["EntraExternalID:ClientSecret"];
        var defaultDomain = configuration["EntraExternalID:DefaultDomain"];

        if (string.IsNullOrEmpty(tenantId))
        {
            errors.Add("EntraExternalID:TenantId is required.");
        }

        if (string.IsNullOrEmpty(clientId))
        {
            errors.Add("EntraExternalID:ClientId is required.");
        }

        if (string.IsNullOrEmpty(clientSecret))
        {
            errors.Add("EntraExternalID:ClientSecret is required.");
        }
        else if (isAzureHosted && !clientSecret.Contains("@Microsoft.KeyVault"))
        {
            errors.Add("EntraExternalID:ClientSecret must use Key Vault reference in Azure environment (@Microsoft.KeyVault).");
        }

        if (string.IsNullOrEmpty(defaultDomain))
        {
            errors.Add("EntraExternalID:DefaultDomain is required.");
        }
    }

    private static void ValidateStorageConfiguration(IConfiguration configuration, List<string> errors)
    {
        var azureWebJobsStorage = configuration["AzureWebJobsStorage"];
        var storageConnectionString = configuration["Storage:ConnectionString"];

        if (string.IsNullOrEmpty(azureWebJobsStorage) && string.IsNullOrEmpty(storageConnectionString))
        {
            errors.Add("Storage connection is required. Set either 'AzureWebJobsStorage' or 'Storage:ConnectionString'.");
        }
    }

    private static void ValidateSendGridConfiguration(IConfiguration configuration, List<string> errors, bool isAzureHosted)
    {
        var apiKey = configuration["SendGrid:ApiKey"];
        var fromEmail = configuration["SendGrid:FromEmail"];
        var passwordResetTemplateId = configuration["SendGrid:PasswordResetTemplateId"];
        var passwordChangedTemplateId = configuration["SendGrid:PasswordChangedTemplateId"];
        var userInvitationTemplateId = configuration["SendGrid:UserInvitationTemplateId"];
        var accountCreatedTemplateId = configuration["SendGrid:AccountCreatedTemplateId"];

        if (string.IsNullOrEmpty(apiKey))
        {
            errors.Add("SendGrid:ApiKey is required.");
        }
        else if (isAzureHosted && !apiKey.Contains("@Microsoft.KeyVault"))
        {
            errors.Add("SendGrid:ApiKey must use Key Vault reference in Azure environment (@Microsoft.KeyVault).");
        }

        if (string.IsNullOrEmpty(fromEmail))
        {
            errors.Add("SendGrid:FromEmail is required.");
        }

        if (string.IsNullOrEmpty(passwordResetTemplateId))
        {
            errors.Add("SendGrid:PasswordResetTemplateId is required.");
        }

        if (string.IsNullOrEmpty(passwordChangedTemplateId))
        {
            errors.Add("SendGrid:PasswordChangedTemplateId is required.");
        }

        if (string.IsNullOrEmpty(userInvitationTemplateId))
        {
            errors.Add("SendGrid:UserInvitationTemplateId is required.");
        }

        if (string.IsNullOrEmpty(accountCreatedTemplateId))
        {
            errors.Add("SendGrid:AccountCreatedTemplateId is required.");
        }
    }

    private static void ValidateUrlConfiguration(IConfiguration configuration, List<string> errors)
    {
        var passwordResetBaseUrl = configuration["PasswordReset:BaseUrl"];
        var accountRegistrationBaseUrl = configuration["AccountRegistration:BaseUrl"];

        if (string.IsNullOrEmpty(passwordResetBaseUrl))
        {
            errors.Add("PasswordReset:BaseUrl is required. Set the base URL for password reset pages.");
        }

        if (string.IsNullOrEmpty(accountRegistrationBaseUrl))
        {
            errors.Add("AccountRegistration:BaseUrl is required. Set the base URL for account registration pages.");
        }
    }

    private static void ValidateKeyVaultConfiguration(IConfiguration configuration, List<string> errors)
    {
        var keyVaultUrl = configuration["KeyVaultUrl"];

        // If Key Vault URL is configured, validate it's a proper URL
        if (!string.IsNullOrEmpty(keyVaultUrl))
        {
            if (!Uri.TryCreate(keyVaultUrl, UriKind.Absolute, out var uri) ||
                !uri.Host.EndsWith(".vault.azure.net", StringComparison.OrdinalIgnoreCase))
            {
                errors.Add("KeyVaultUrl must be a valid Azure Key Vault URL (format: https://your-vault-name.vault.azure.net/).");
            }
        }
    }

    /// <summary>
    /// Validates that optional configuration values with defaults are within acceptable ranges.
    /// This method logs warnings for values outside recommended ranges but does not throw exceptions.
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <param name="logger">Logger for warning messages</param>
    public static void ValidateOptionalConfiguration(IConfiguration configuration, ILogger logger)
    {
        // Validate PasswordHistory settings
        var maxCount = configuration.GetValue<int>("PasswordHistory:MaxCount", 12);
        var workFactor = configuration.GetValue<int>("PasswordHistory:WorkFactor", 12);

        if (maxCount < 1 || maxCount > 50)
        {
            logger.LogWarning("PasswordHistory:MaxCount value {MaxCount} is outside recommended range (1-50). Using value anyway.", maxCount);
        }

        if (workFactor < 10 || workFactor > 15)
        {
            logger.LogWarning("PasswordHistory:WorkFactor value {WorkFactor} is outside recommended range (10-15). Using value anyway.", workFactor);
        }

        // Validate RateLimit settings
        var maxRequestsPerMinute = configuration.GetValue<int>("RateLimit:MaxRequestsPerMinute", 60);
        if (maxRequestsPerMinute < 1 || maxRequestsPerMinute > 1000)
        {
            logger.LogWarning("RateLimit:MaxRequestsPerMinute value {MaxRequests} is outside recommended range (1-1000). Using value anyway.", maxRequestsPerMinute);
        }

        // Validate Invitation settings
        var tokenExpirationDays = configuration.GetValue<int>("Invitation:TokenExpirationDays", 45);
        if (tokenExpirationDays < 1 || tokenExpirationDays > 365)
        {
            logger.LogWarning("Invitation:TokenExpirationDays value {ExpirationDays} is outside recommended range (1-365). Using value anyway.", tokenExpirationDays);
        }
    }
}
