using Microsoft.Extensions.Configuration;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Provides centralized configuration validation with fail-fast behavior.
/// All required configuration values are validated at startup with clear error messages.
/// </summary>
public static class ConfigurationValidator
{
    /// <summary>
    /// Validates all required configuration values and throws InvalidOperationException 
    /// with clear error messages if any are missing or invalid.
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <exception cref="InvalidOperationException">Thrown when required configuration is missing or invalid</exception>
    public static void ValidateRequiredConfiguration(IConfiguration configuration)
    {
        var errors = new List<string>();

        // Validate Entra External ID configuration
        ValidateEntraConfiguration(configuration, errors);

        // Validate Storage configuration
        ValidateStorageConfiguration(configuration, errors);

        // Validate SendGrid configuration
        ValidateSendGridConfiguration(configuration, errors);

        // Validate URL configurations
        ValidateUrlConfiguration(configuration, errors);

        // Validate Key Vault configuration (conditional)
        ValidateKeyVaultConfiguration(configuration, errors);

        if (errors.Any())
        {
            var errorMessage = "Required configuration values are missing or invalid:\n\n" +
                              string.Join("\n", errors.Select(e => $"• {e}")) +
                              "\n\nPlease configure these values in your application settings, local.settings.json, or Azure Key Vault.";
            
            throw new InvalidOperationException(errorMessage);
        }
    }

    private static void ValidateEntraConfiguration(IConfiguration configuration, List<string> errors)
    {
        var tenantId = configuration["EntraExternalID:TenantId"];
        var clientId = configuration["EntraExternalID:ClientId"];
        var clientSecret = configuration["EntraExternalID:ClientSecret"];
        var defaultDomain = configuration["EntraExternalID:DefaultDomain"];

        if (string.IsNullOrEmpty(tenantId) || tenantId == "REPLACE_WITH_YOUR_ACTUAL_TENANT_ID")
        {
            errors.Add("EntraExternalID:TenantId is required.");
        }

        if (string.IsNullOrEmpty(clientId) || clientId == "REPLACE_WITH_YOUR_ACTUAL_CLIENT_ID")
        {
            errors.Add("EntraExternalID:ClientId is required. ");
        }

        if (string.IsNullOrEmpty(clientSecret) || clientSecret == "REPLACE_WITH_YOUR_ACTUAL_CLIENT_SECRET" || clientSecret == "temp-local-dev-secret")
        {
            errors.Add("EntraExternalID:ClientSecret is required.");
        }

        if (string.IsNullOrEmpty(defaultDomain) || defaultDomain == "yourtenant.onmicrosoft.com")
        {
            errors.Add("EntraExternalID:DefaultDomain is required.");
        }
    }

    private static void ValidateStorageConfiguration(IConfiguration configuration, List<string> errors)
    {
        var azureWebJobsStorage = configuration["AzureWebJobsStorage"];
        var storageConnectionString = configuration["Storage:ConnectionString"];

        if (string.IsNullOrEmpty(azureWebJobsStorage) && string.IsNullOrEmpty(storageConnectionString))
        {
            errors.Add("Storage connection is required. Set either 'AzureWebJobsStorage' or 'Storage:ConnectionString'.");
        }
    }

    private static void ValidateSendGridConfiguration(IConfiguration configuration, List<string> errors)
    {
        var apiKey = configuration["SendGrid:ApiKey"];
        var fromEmail = configuration["SendGrid:FromEmail"];
        var passwordResetTemplateId = configuration["SendGrid:PasswordResetTemplateId"];
        var passwordChangedTemplateId = configuration["SendGrid:PasswordChangedTemplateId"];
        var userInvitationTemplateId = configuration["SendGrid:UserInvitationTemplateId"];
        var accountCreatedTemplateId = configuration["SendGrid:AccountCreatedTemplateId"];

        if (string.IsNullOrEmpty(apiKey) || apiKey == "REPLACE_WITH_YOUR_SENDGRID_API_KEY" || apiKey == "temp-local-dev-key")
        {
            errors.Add("SendGrid:ApiKey is required. Set your SendGrid API key (consider using Azure Key Vault).");
        }

        if (string.IsNullOrEmpty(fromEmail))
        {
            errors.Add("SendGrid:FromEmail is required. Set the verified sender email address.");
        }

        if (string.IsNullOrEmpty(passwordResetTemplateId) || passwordResetTemplateId.StartsWith("d-placeholder") || passwordResetTemplateId == "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
        {
            errors.Add("SendGrid:PasswordResetTemplateId is required. Set your SendGrid dynamic template ID for password reset emails.");
        }

        if (string.IsNullOrEmpty(passwordChangedTemplateId) || passwordChangedTemplateId.StartsWith("d-placeholder") || passwordChangedTemplateId == "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
        {
            errors.Add("SendGrid:PasswordChangedTemplateId is required. Set your SendGrid dynamic template ID for password changed notifications.");
        }

        if (string.IsNullOrEmpty(userInvitationTemplateId) || userInvitationTemplateId.StartsWith("d-placeholder") || userInvitationTemplateId == "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
        {
            errors.Add("SendGrid:UserInvitationTemplateId is required. Set your SendGrid dynamic template ID for user invitation emails.");
        }

        if (string.IsNullOrEmpty(accountCreatedTemplateId) || accountCreatedTemplateId.StartsWith("d-placeholder") || accountCreatedTemplateId == "d-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
        {
            errors.Add("SendGrid:AccountCreatedTemplateId is required. Set your SendGrid dynamic template ID for account created notifications.");
        }
    }

    private static void ValidateUrlConfiguration(IConfiguration configuration, List<string> errors)
    {
        var passwordResetBaseUrl = configuration["PasswordReset:BaseUrl"];
        var accountRegistrationBaseUrl = configuration["AccountRegistration:BaseUrl"];

        if (string.IsNullOrEmpty(passwordResetBaseUrl))
        {
            errors.Add("PasswordReset:BaseUrl is required. Set the base URL for password reset pages.");
        }

        if (string.IsNullOrEmpty(accountRegistrationBaseUrl))
        {
            errors.Add("AccountRegistration:BaseUrl is required. Set the base URL for account registration pages.");
        }
    }

    private static void ValidateKeyVaultConfiguration(IConfiguration configuration, List<string> errors)
    {
        // Check if any configuration values reference Key Vault
        var hasKeyVaultReferences = HasKeyVaultReferences(configuration);
        var keyVaultUrl = configuration["KeyVaultUrl"];

        if (hasKeyVaultReferences && string.IsNullOrEmpty(keyVaultUrl))
        {
            errors.Add("KeyVaultUrl is required when using Azure Key Vault references (@Microsoft.KeyVault). Set the Key Vault URL or remove Key Vault references.");
        }

        if (!string.IsNullOrEmpty(keyVaultUrl) && keyVaultUrl == "https://your-keyvault-name.vault.azure.net/")
        {
            errors.Add("KeyVaultUrl contains placeholder value. Set your actual Azure Key Vault URL.");
        }
    }

    private static bool HasKeyVaultReferences(IConfiguration configuration)
    {
        // Check all configuration values for Key Vault references
        foreach (var kvp in configuration.AsEnumerable())
        {
            if (!string.IsNullOrEmpty(kvp.Value) && kvp.Value.Contains("@Microsoft.KeyVault"))
            {
                return true;
            }
        }
        return false;
    }

    public static void ValidateOptionalConfiguration(IConfiguration configuration, Microsoft.Extensions.Logging.ILogger logger)
    {
        // Validate PasswordHistory settings
        var maxCount = configuration.GetValue<int>("PasswordHistory:MaxCount", 12);
        var workFactor = configuration.GetValue<int>("PasswordHistory:WorkFactor", 12);

        if (maxCount < 1 || maxCount > 50)
        {
            logger.LogWarning("PasswordHistory:MaxCount value {MaxCount} is outside recommended range (1-50). Using value anyway.", maxCount);
        }

        if (workFactor < 10 || workFactor > 15)
        {
            logger.LogWarning("PasswordHistory:WorkFactor value {WorkFactor} is outside recommended range (10-15). Using value anyway.", workFactor);
        }

        // Validate RateLimit settings
        var maxRequestsPerMinute = configuration.GetValue<int>("RateLimit:MaxRequestsPerMinute", 60);
        if (maxRequestsPerMinute < 1 || maxRequestsPerMinute > 1000)
        {
            logger.LogWarning("RateLimit:MaxRequestsPerMinute value {MaxRequests} is outside recommended range (1-1000). Using value anyway.", maxRequestsPerMinute);
        }

        // Validate Invitation settings
        var tokenExpirationDays = configuration.GetValue<int>("Invitation:TokenExpirationDays", 45);
        if (tokenExpirationDays < 1 || tokenExpirationDays > 365)
        {
            logger.LogWarning("Invitation:TokenExpirationDays value {ExpirationDays} is outside recommended range (1-365). Using value anyway.", tokenExpirationDays);
        }
    }
}
