# PowerPagesCustomAuth - Current Architecture Summary

**Last Updated:** January 14, 2025  
**Version:** 4-Function Architecture (Post AuthenticationFunction Removal)

---

## Overview

PowerPagesCustomAuth is a streamlined Azure Functions application that provides password management and user registration capabilities for Power Pages applications integrated with Entra External ID.

## Core Architecture

### Azure Functions (4 Functions)

1. **PasswordService** - Password operations and validation
2. **RegistrationService** - User account creation with invitation validation  
3. **UtilityService** - System health checks, cleanup, and notifications
4. **InvitationService** - User invitation management

### Authentication Flow

**Primary Authentication:** Entra External ID (Native Power Pages Integration)
- Users authenticate via `/.auth/login/EntraExternalID`
- No custom authentication endpoints
- Session management handled by Power Pages
- Password operations handled separately via PasswordService

## Function Details

### 1. PasswordService

**Endpoint:** `/api/PasswordService`

**Operations:**
- `validate` - Password strength and history validation
- `update-history` - Update password history after change
- `reset-initiate` - Start password reset flow
- `reset-complete` - Complete password reset with token

**Key Features:**
- BCrypt password hashing
- Password history validation (configurable count)
- Email notifications via SendGrid
- Token-based reset flow with verification codes

### 2. RegistrationService

**Endpoint:** `/api/RegistrationService`

**Operations:**
- `register` - Create new user account with invitation validation

**Key Features:**
- Invitation token validation
- Entra External ID user creation
- Password history initialization
- Email notifications for account creation

### 3. UtilityService

**Endpoint:** `/api/UtilityService`

**Operations:**
- `health` - System health check
- `cleanup-tokens` - Remove expired tokens
- `stats` - System statistics
- `notify-expiring-passwords` - Send expiration notifications

**Key Features:**
- Service health monitoring
- Proactive password expiration notifications
- Token cleanup and maintenance
- System statistics and reporting

### 4. InvitationService

**Endpoint:** `/api/InvitationService`

**Operations:**
- `invite-user` - Send user invitation
- `validate-token` - Validate invitation token

**Key Features:**
- Secure invitation token generation
- Email invitation delivery
- Token expiration management
- Invitation validation

## Supporting Services

### Core Services
- **PasswordHistoryService** - Password history management with Azure Blob Storage
- **EmailService** - SendGrid integration for email notifications
- **ResetTokenManager** - Password reset token management
- **InvitationTokenManager** - User invitation token management
- **RateLimitService** - API rate limiting

### Shared Components
- **BaseFunctionService** - Common response handling and CORS
- **ConfigurationOptions** - Strongly-typed configuration classes
- **Result<T>** - Standardized response wrapper

## Data Storage

### Azure Blob Storage
- **Container:** `passwordhistory`
- **Format:** JSON files per user (`{scopedUserId}.json`)
- **Content:** Password hashes with metadata (LastUpdatedUtc)

### In-Memory Cache
- **Reset Tokens** - 15-minute expiration
- **Invitation Tokens** - Configurable expiration (default: 45 days)
- **Rate Limiting** - Request tracking per IP/user

## External Integrations

### Microsoft Graph API
- **User Management** - Create, update, query users
- **Password Operations** - Force password changes
- **Application Context** - Department field for app scoping

### SendGrid Email Service
- **Templates:**
  - Password Reset
  - Password Changed Notification
  - User Invitation
  - Account Created
  - Password Expiration Warning
  - Password Expired Alert

### Entra External ID
- **Authentication** - Primary user authentication
- **User Storage** - User account management
- **Session Management** - Handled by Power Pages

## Power Pages Integration

### JavaScript Files
- `registration.js` - User registration form handling
- `forgot-password.js` - Password reset initiation
- `reset-password.js` - Password reset completion
- `send-invitation.js` - Admin invitation sending
- `invitation-error.js` - Error handling for invitations

### Configuration
- **Meta Tags** - Secure configuration access via Liquid templates
- **Function URLs** - Dynamic endpoint configuration
- **MSAL Integration** - Entra External ID client configuration

## Security Features

### Authentication & Authorization
- **Function Keys** - Azure Function-level security
- **CORS Configuration** - Restricted origins and headers
- **Input Validation** - Comprehensive sanitization
- **Rate Limiting** - Request throttling

### Password Security
- **BCrypt Hashing** - Configurable work factor (default: 12)
- **History Validation** - Prevent password reuse (default: 12 passwords)
- **Strength Requirements** - Configurable complexity rules
- **Expiration Notifications** - Proactive user alerts

### Token Security
- **Secure Generation** - Cryptographically secure random tokens
- **Time-based Expiration** - Automatic token invalidation
- **Single Use** - Tokens marked as used after consumption
- **Verification Codes** - Additional security layer

## Configuration

### Environment Variables
```
# Core Settings
ApplicationName
PasswordHistory:MaxCount (default: 12)
PasswordHistory:WorkFactor (default: 12)
RateLimit:MaxRequestsPerMinute (default: 60)

# Entra External ID
EntraExternalID:TenantId
EntraExternalID:ClientId
EntraExternalID:ClientSecret
EntraExternalID:DefaultDomain

# SendGrid
SendGrid:ApiKey
SendGrid:FromEmail
SendGrid:*TemplateId (various templates)

# URLs
PasswordReset:BaseUrl
AccountRegistration:BaseUrl

# Storage
Storage:ConnectionString
KeyVaultUrl (optional)
```

### Power Pages Site Settings
```
AzureFunctionUrl
ApplicationName
MSALClientId
MSALTenantId
Password Function Key
Registration Function Key
Utility Function Key
Invitation Function Key
```

## Monitoring & Logging

### Application Insights
- **Correlation IDs** - Request tracking across functions
- **Performance Metrics** - Function execution times
- **Error Tracking** - Exception logging and alerting
- **Custom Events** - Business logic tracking

### Health Monitoring
- **Service Health Checks** - Blob Storage, Graph API, Email Service
- **Automatic Retries** - Configurable retry policies
- **Circuit Breaker** - Failure threshold monitoring

## Deployment

### Build Process
- **.NET 8 Isolated Worker** - Modern Azure Functions runtime
- **Dependency Injection** - Comprehensive service registration
- **Configuration Validation** - Startup-time validation

### Deployment Targets
- **Azure Function App** - Primary hosting platform
- **Power Pages Site** - Frontend integration
- **Azure Blob Storage** - Data persistence
- **Azure Key Vault** - Secure configuration (optional)

## Recent Changes

### AuthenticationFunction Removal (January 14, 2025)
- **Removed:** Custom authentication endpoints
- **Reason:** Redundant with Entra External ID native authentication
- **Impact:** None - no functional dependencies identified
- **Benefits:** Simplified architecture, reduced maintenance, clearer responsibilities

## Testing Strategy

### Manual Testing
- Power Pages user flows (registration, password reset, login)
- Admin invitation workflows
- Error handling scenarios

### API Testing
- Direct function endpoint testing
- Integration testing across functions
- Security and validation testing

### Automated Testing
- Unit tests for core business logic
- Integration tests for external services
- Health check monitoring

---

This architecture provides a robust, secure, and maintainable solution for password management and user registration while leveraging Entra External ID for primary authentication capabilities.
