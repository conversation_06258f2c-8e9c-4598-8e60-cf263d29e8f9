# Quick Start Guide

Get the PowerPages Custom Authentication system running in 30 minutes.

## 🎯 **Prerequisites**

- Azure subscription with admin access
- Power Pages site
- VS Code with Azure Functions extension
- .NET 8 SDK

## ⚡ **5-Minute Local Setup**

### **1. <PERSON><PERSON> and Configure**
```bash
# Build the project
dotnet restore
dotnet build

# Copy and edit configuration
cp local.settings.json.template local.settings.json
```

### **2. Update local.settings.json**
```json
{
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated"
  },
  "EntraExternalID": {
    "TenantId": "YOUR_TENANT_ID",
    "ClientId": "YOUR_CLIENT_ID", 
    "ClientSecret": "YOUR_CLIENT_SECRET",
    "DefaultDomain": "yourtenant.onmicrosoft.com"
  },
  "SendGrid": {
    "ApiKey": "SG.YOUR_SENDGRID_API_KEY",
    "FromEmail": "<EMAIL>"
  },
  "PasswordReset": {
    "BaseUrl": "https://your-powerpage.powerappsportals.com"
  },
  "Storage": {
    "ConnectionString": "UseDevelopmentStorage=true"
  }
}
```

**📧 SendGrid Setup Required**: See [SendGrid Setup Guide](../../SENDGRID_SETUP_GUIDE.md) for email configuration.

### **3. Start Local Development**
```bash
# Start Azure Storage Emulator (if using local storage)
# Start the function app
func start
```

✅ **Test**: Visit `http://localhost:7071/api/UtilityService?operation=health`

## 🚀 **15-Minute Azure Setup**

### **1. Create Azure Resources**

**Function App**:
```bash
az functionapp create \
  --resource-group your-rg \
  --consumption-plan-location eastus \
  --runtime dotnet-isolated \
  --functions-version 4 \
  --name your-auth-functions \
  --storage-account yourstorageaccount
```

**Storage Account** (if not existing):
```bash
az storage account create \
  --name yourstorageaccount \
  --resource-group your-rg \
  --location eastus \
  --sku Standard_LRS
```

### **2. Deploy Functions**

**Via VS Code**:
1. Open project in VS Code
2. Press `Ctrl+Shift+P`
3. Type "Azure Functions: Deploy to Function App"
4. Select your Function App
5. Confirm deployment

### **3. Configure Function App Settings**

**Essential Settings**:
```bash
# Core configuration
az functionapp config appsettings set --name your-auth-functions --resource-group your-rg --settings \
  "EntraExternalID:TenantId=YOUR_TENANT_ID" \
  "EntraExternalID:ClientId=YOUR_CLIENT_ID" \
  "EntraExternalID:ClientSecret=YOUR_CLIENT_SECRET" \
  "SMTP2GOApiKey=api-YOUR_SMTP2GO_KEY" \
  "SMTP2GOFromEmail=<EMAIL>" \
  "ResetPasswordBaseUrl=https://your-powerpage.powerappsportals.com"
```

✅ **Test**: Visit `https://your-auth-functions.azurewebsites.net/api/UtilityService?operation=health`

## 🔧 **10-Minute Power Pages Setup**

### **1. Configure Site Settings**

In Power Pages Admin Portal → Setup → Site Settings:

```
AzureFunctionUrl = https://your-auth-functions.azurewebsites.net/api
ApplicationName = Your Application Name
MSALClientId = YOUR_CLIENT_ID
MSALTenantId = YOUR_TENANT_ID
```

### **2. Upload Custom Pages**

**Create these pages in Power Pages**:
- `/Custom-User-Registration` → Upload `registration.html`
- `/Custom-Forgot-Password` → Upload `forgot-password.html`  
- `/Custom-Password-Reset` → Upload `reset-password.html`

**Upload supporting files**:
- `registration.js`
- `forgot-password.js`
- `reset-password.js`
- `power-pages-styles.css`

### **3. Configure Authentication**

**Power Pages Admin → Authentication**:
```
Default Identity Provider: EntraExternalID
Require Authentication: Yes
Enable Registration: No (use custom registration)
```

✅ **Test**: Try registering a new user through your custom registration page

## 🔐 **Essential Security (BEFORE PRODUCTION)**

### **⚠️ CRITICAL: Change Authorization Levels**

**Update these files and redeploy**:
```csharp
// AuthenticationService.cs, PasswordService.cs, UtilityService.cs
[HttpTrigger(AuthorizationLevel.Function, ...)]  // Change from Anonymous
```

### **⚠️ CRITICAL: Configure CORS**

**Update host.json**:
```json
{
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": ["https://your-actual-domain.powerappsportals.com"],
        "allowedMethods": ["GET", "POST", "OPTIONS"]
      }
    }
  }
}
```

### **Get Function Keys**

After changing to Function authorization:
```bash
# Get the function key from Azure Portal
# Update Power Pages with the key:
AzureFunctionKey = YOUR_FUNCTION_KEY
```

## 🧪 **Testing Your Setup**

### **1. Health Check**
```bash
curl "https://your-auth-functions.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_KEY"
```

### **2. Test User Registration**
1. Go to `/Custom-User-Registration`
2. Fill out the form
3. Check that user is created in Entra External ID
4. Verify password history is stored

### **3. Test Password Reset**
1. Go to `/Custom-Forgot-Password`
2. Enter email address
3. Check email for verification code
4. Complete reset on `/Custom-Password-Reset`

### **4. Test Standard Login**
1. Go to any protected page
2. Should redirect to Entra External ID login
3. Login with created user
4. Should return to Power Pages authenticated

## 🚨 **Common Issues**

### **"Function not found" errors**
- Check function app is running
- Verify function names match exactly
- Check CORS configuration

### **"401 Unauthorized" errors**
- Verify function keys are correct
- Check authorization level settings
- Ensure Power Pages has the right function key

### **Email not sending**
- Check SMTP2GO API key format (starts with `api-`)
- Verify sender email is authenticated in SMTP2GO
- Check Application Insights logs for email errors

### **CORS errors in browser**
- Update `allowedOrigins` in host.json
- Redeploy after CORS changes
- Clear browser cache

## 📊 **Monitoring Setup**

### **Quick Application Insights**
```bash
# Create Application Insights
az monitor app-insights component create \
  --app your-auth-insights \
  --location eastus \
  --resource-group your-rg

# Add to Function App
az functionapp config appsettings set \
  --name your-auth-functions \
  --resource-group your-rg \
  --settings "APPINSIGHTS_INSTRUMENTATIONKEY=YOUR_KEY"
```

### **Essential Queries**
```kusto
// Error rate
requests | summarize ErrorRate = (countif(resultCode >= "400") * 100.0) / count()

// Response times
requests | summarize avg(duration) by name

// Recent errors
exceptions | top 10 by timestamp desc
```

## 🎉 **You're Ready!**

Your authentication system is now running with:
- ✅ Custom registration with password history
- ✅ Standard Entra External ID login
- ✅ Password reset with email verification
- ✅ Basic monitoring and health checks

## 🔄 **Next Steps**

1. **Production Security**: Follow the Security Essentials guide
2. **Advanced Monitoring**: Set up alerts and dashboards
3. **Performance Tuning**: Monitor and optimize based on usage
4. **Backup Strategy**: Configure backup and disaster recovery

## 📞 **Need Help?**

- Check the Troubleshooting Guide for common issues
- Use `UtilityService?operation=config-check` to validate configuration
- Review Application Insights logs for detailed error information
