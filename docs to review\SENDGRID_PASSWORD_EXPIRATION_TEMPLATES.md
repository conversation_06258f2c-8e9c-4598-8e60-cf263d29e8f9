# SendGrid Password Expiration Templates

This document provides the SendGrid dynamic templates for password expiration notifications.

## Template 1: Password Expiration Warning

**Template Name**: Password Expiration Warning
**Template ID**: `d-[to-be-generated]`
**Subject**: Password Expires in {{daysUntilExpiration}} Days - {{applicationName}}

### HTML Content

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Expiration Notice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #dc143c;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #dc143c;
            margin: 0;
            font-size: 24px;
        }
        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .warning-box h2 {
            color: #856404;
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .days-remaining {
            font-size: 36px;
            font-weight: bold;
            color: #dc143c;
            margin: 10px 0;
        }
        .action-button {
            display: inline-block;
            background-color: #dc143c;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
        }
        .action-button:hover {
            background-color: #b91c3c;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .instructions h3 {
            color: #333;
            margin-top: 0;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Expiration Notice</h1>
        </div>
        
        <div class="warning-box">
            <h2>Your password will expire in:</h2>
            <div class="days-remaining">{{daysUntilExpiration}} day{{#if (gt daysUntilExpiration 1)}}s{{/if}}</div>
        </div>
        
        <p>Hello,</p>
        
        <p>Your password for <strong>{{applicationName}}</strong> will expire in <strong>{{daysUntilExpiration}} day{{#if (gt daysUntilExpiration 1)}}s{{/if}}</strong>.</p>
        
        <p>To avoid losing access to your account, please reset your password now.</p>
        
        <div style="text-align: center;">
            <a href="{{forgotPasswordUrl}}" class="action-button">Reset Password Now</a>
        </div>
        
        <div class="instructions">
            <h3>How to Reset Your Password:</h3>
            <ol>
                <li>Click the "Reset Password Now" button above</li>
                <li>Enter your email address: <strong>{{email}}</strong></li>
                <li>Check your email for reset instructions</li>
                <li>Follow the link to set your new password</li>
            </ol>
        </div>
        
        <p><strong>Important:</strong> If you don't reset your password before it expires, you will be unable to access your account until you complete the password reset process.</p>
        
        <div class="footer">
            <p>This is an automated notification. Please do not reply to this email.</p>
            <p>If you have questions, please contact support.</p>
            <p>Notification sent on {{notificationDate}} | Correlation ID: {{correlationId}}</p>
        </div>
    </div>
</body>
</html>
```

### Text Content

```
Password Expiration Notice - {{applicationName}}

Your password will expire in {{daysUntilExpiration}} day(s).

To reset your password:
1. Visit: {{forgotPasswordUrl}}
2. Enter your email: {{email}}
3. Follow the reset instructions

Reset your password now to avoid losing access to your account.

This is an automated notification. Please do not reply.
Correlation ID: {{correlationId}}
```

## Template 2: Password Expired

**Template Name**: Password Expired - Reset Required
**Template ID**: `d-[to-be-generated]`
**Subject**: Password Expired - Reset Required - {{applicationName}}

### HTML Content

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Expired</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #dc143c;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #dc143c;
            margin: 0;
            font-size: 24px;
        }
        .alert-box {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .alert-box h2 {
            color: #721c24;
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .expired-text {
            font-size: 24px;
            font-weight: bold;
            color: #dc143c;
            margin: 10px 0;
        }
        .action-button {
            display: inline-block;
            background-color: #dc143c;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
        }
        .action-button:hover {
            background-color: #b91c3c;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .instructions h3 {
            color: #333;
            margin-top: 0;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Expired</h1>
        </div>
        
        <div class="alert-box">
            <h2>Account Access Suspended</h2>
            <div class="expired-text">Password Expired</div>
        </div>
        
        <p>Hello,</p>
        
        <p>Your password for <strong>{{applicationName}}</strong> has expired. You cannot access your account until you reset your password.</p>
        
        <div style="text-align: center;">
            <a href="{{forgotPasswordUrl}}" class="action-button">Reset Password Now</a>
        </div>
        
        <div class="instructions">
            <h3>To Regain Access:</h3>
            <ol>
                <li>Click the "Reset Password Now" button above</li>
                <li>Enter your email address: <strong>{{email}}</strong></li>
                <li>Check your email for reset instructions</li>
                <li>Follow the link to set your new password</li>
                <li>Log in with your new password</li>
            </ol>
        </div>
        
        <p><strong>Security Note:</strong> Regular password changes help protect your account. Your new password will be valid for the configured period before requiring another reset.</p>
        
        <div class="footer">
            <p>This is an automated notification. Please do not reply to this email.</p>
            <p>If you have questions, please contact support.</p>
            <p>Password expired on {{expiredDate}} | Correlation ID: {{correlationId}}</p>
        </div>
    </div>
</body>
</html>
```

### Text Content

```
Password Expired - {{applicationName}}

Your password has expired. You cannot access your account until you reset it.

To regain access:
1. Visit: {{forgotPasswordUrl}}
2. Enter your email: {{email}}
3. Follow the reset instructions

Reset your password immediately to restore access.

This is an automated notification. Please do not reply.
Correlation ID: {{correlationId}}
```

## Template Variables

### Password Expiration Warning Template
- `applicationName`: Name of the application
- `daysUntilExpiration`: Number of days until password expires
- `email`: User's email address
- `forgotPasswordUrl`: URL to the forgot password page
- `websiteUrl`: Base website URL
- `notificationDate`: Date the notification was sent
- `correlationId`: Tracking ID for the notification

### Password Expired Template
- `applicationName`: Name of the application
- `email`: User's email address
- `forgotPasswordUrl`: URL to the forgot password page
- `websiteUrl`: Base website URL
- `expiredDate`: Date the password expired
- `correlationId`: Tracking ID for the notification

## Configuration

After creating these templates in SendGrid, add the template IDs to your configuration:

```json
{
  "SendGrid": {
    "PasswordExpirationTemplateId": "d-your-expiration-template-id",
    "PasswordExpiredTemplateId": "d-your-expired-template-id"
  }
}
```

## Environment Variables

Add these to your Azure Function App settings:

```
PASSWORD_EXPIRATION_DAYS=45
PASSWORD_WARNING_DAYS=7
SendGrid__PasswordExpirationTemplateId=d-your-expiration-template-id
SendGrid__PasswordExpiredTemplateId=d-your-expired-template-id
```
