Configuration Note – Power Pages Static File Settings
Date: July 5 2025

We discovered that Liquid tags inside static Web Files are not rendered by Power Pages.  To make static HTML/JS (e.g., registration.html / registration.js) receive site-settings values, we must expose those settings as meta tags in the site layout header.

Action taken:
• Added the following meta tags inside the <head> section of the portal header layout:
  <meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] }}">
  <meta name="msal-client-id"    content="{{ settings['MSALClientId'] }}">
  <meta name="msal-tenant-id"    content="{{ settings['MSALTenantId'] }}">
  <meta name="application-name"  content="{{ settings['ApplicationName'] }}">
  <meta name="application-id"    content="{{ settings['ApplicationId'] }}">
• Published the layout.  Static JS now reads these values via document.querySelector meta look-ups.

This step is REQUIRED for correct configuration on every environment.

Additional implementation notes:
• `registration.js` (and any other static script) now resolves configuration in this order:
  1. `window.appConfig` (if page is a Web Template/Page that sets it).
  2. The meta tags above via `document.querySelector('meta[name="…"]').content`.
  3. Safe fallback constants ("default-application", "Power Pages Application" etc.).
• Ensure your `<script src="/registration.js">` tag is placed **after** the meta tags in the HTML output so the DOM elements are available when the file loads.  In the standard Power Pages layout this is already true because meta tags live in `<head>` and Web Files are loaded at the end of the body.
• When cloning this solution to another site, remember to copy the meta-tag block into the target layout before uploading the static files.

