{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}, "logLevel": {"default": "Information", "Function": "Information", "Host.Results": "Information", "Host.Aggregator": "Information", "PasswordHistoryValidator": "Information"}}, "extensions": {"http": {"routePrefix": "api", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true}}, "functionTimeout": "00:05:00", "healthMonitor": {"enabled": true, "healthCheckInterval": "00:00:10", "healthCheckWindow": "00:02:00", "healthCheckThreshold": 6, "counterThreshold": 0.8}, "retry": {"strategy": "fixedDelay", "maxRetryCount": 3, "delayInterval": "00:00:05"}}