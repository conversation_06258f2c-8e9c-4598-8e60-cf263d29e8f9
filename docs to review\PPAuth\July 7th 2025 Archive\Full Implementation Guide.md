Information about the pure Entra External ID authentication system that combines:

- **Pure Entra External ID authentication** for login (redirect-based, managed by Power Pages)
- **Custom password operations** for registration, forgot password, and password reset (maintaining password history)
- **Enhanced user management** with application-specific data and v1.0 API integration
- **Complete email functionality** with direct SMTP2GO integration

This approach maximizes simplicity by using standard Entra External ID login while maintaining all required password history functionality and providing enterprise-grade security.

## Architecture Overview

### High-Level Architecture

```mermaid
flowchart TD
    A[User] --> B{Authentication Type}

    B -->|Login| C[Standard Entra External ID]
    C --> D[Power Pages Authentication]
    D --> E[Authenticated User Session]

    B -->|Registration| F[Custom Registration Page]
    F --> G[AuthenticationService Azure Function]
    G --> H[Entra External ID + Password History]

    B -->|Forgot Password| I[Custom Forgot Password Page]
    I --> J[PasswordService Azure Function]
    J --> K[Reset Token + Email]

    B -->|Reset Password| L[Custom Reset Password Page]
    L --> M[PasswordService Azure Function]
    M --> N[Password History Validation]
    N --> O[Update Password + History]

    B -->|Password Management| P[Unified Password Management Page]
    P --> Q[PasswordService Azure Function]
    Q --> R[Token/Current Password Verification]
    R --> S[Password History Validation]
    S --> T[Update Password + History]
```

### Components

#### 1. Pure Entra External ID Authentication

- **Login**: Direct redirect to Entra External ID (/.auth/login/[provider])
- **Session Management**: Power Pages (built-in)
- **Security**: Microsoft-managed infrastructure
- **No Custom Validation**: Login bypasses Azure Functions entirely

#### 2. Custom Password Operations (Enhanced)

- **Registration**: Custom page with password history initialization and application context
- **Forgot Password**: Custom page with secure token generation and email delivery
- **Password Management**: Unified page handling both password reset (token-based) and password change (current password verification) with history validation

#### 3. Azure Functions

- **AuthenticationService**: User registration and user management (login operations exist but unused by frontend)
- **PasswordService**: All password operations with email integration and comprehensive history validation
- **UtilityService**: Health checks, configuration validation, and system maintenance

**Note**: AuthenticationService contains login/credential validation methods, but the current frontend implementation uses pure Entra External ID redirect instead.

## Implementation Details

### 1. Login

**User Experience**:

1. User navigates to protected content
2. Power Pages automatically redirects to standard Entra External ID authentication
3. Standard Entra External ID authentication (Microsoft-managed)
4. After authentication, returned to Power Pages as authenticated user

**Technical Flow**:

- No Azure Function calls for login
- Pure redirect-based authentication
- All credential validation handled by Microsoft

### 2.Registration

**User Experience**:

1. User clicks "Create New Account" from login page
2. Navigates to `/Custom-User-Registration`
3. Fills out registration form with password and optional application context
4. Password validated against history requirements
5. User created in Entra External ID with enhanced attributes and password history initialized
6. Automatic redirect to standard login

## Features and API Migration

### 1. Microsoft Graph v1.0 API

Moved from beta to v1.0 for production stability

**Changes Made**:

```csharp
// BEFORE
var graphApiEndpoint = Environment.GetEnvironmentVariable("GRAPH_API_ENDPOINT") ?? "https://graph.microsoft.com/beta";

// AFTER
var graphApiEndpoint = Environment.GetEnvironmentVariable("GRAPH_API_ENDPOINT") ?? "https://graph.microsoft.com/v1.0";
```

### 2. Enhanced User Registration with Application Context

**New Extension Attributes**:

```csharp
// Core application association
extension_ApplicationId          // Required: Links user to specific application
extension_RegistrationDate       // Timestamp of registration
extension_RegistrationSource     // Source: "PowerPages", "DirectAPI", "AdminPortal"

// Optional enhanced attributes
extension_ApplicationName        // Human-readable application name
extension_Department            // User's department/organizational unit
extension_UserRole              // Role within application context

// Password policy tracking
extension_PasswordPolicyLevel   // "Standard", "Enhanced", "Custom"
extension_LastPasswordHistoryCheck // Last password history validation
```

### 3. Advanced Email Resolution

**Enhanced Filtering**:

```csharp
// BEFORE
requestConfiguration.QueryParameters.Filter = $"mail eq '{email}'";

// AFTER
requestConfiguration.QueryParameters.Filter =
    $"mail eq '{email}' or userPrincipalName eq '{email}' or proxyAddresses/any(c:c eq 'SMTP:{email}')";
```

**Benefits**:

- Resolves users by primary email, UPN, or proxy addresses
- Handles complex email scenarios (aliases, forwarding)

### 4. Application-Specific User Management

**New Helper Methods**:

```csharp
// Find user by email within application context
public static async Task<User> FindUserByEmailWithApplicationContextAsync(
    GraphServiceClient graphClient,
    string email,
    string applicationId,
    CancellationToken cancellationToken = default)
{
    var users = await graphClient.Users
        .GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Filter =
                $"(mail eq '{email}' or userPrincipalName eq '{email}' or proxyAddresses/any(c:c eq 'SMTP:{email}')) " +
                $"and extension_ApplicationId eq '{applicationId}'";

            requestConfiguration.QueryParameters.Select = new[] {
                "id", "userPrincipalName", "mail", "displayName",
                "extension_ApplicationId", "extension_ApplicationName",
                "extension_RegistrationSource", "extension_Department", "extension_UserRole"
            };
        }, cancellationToken);

    return users?.Value?.FirstOrDefault();
}

// Get all users for a specific application
public static async Task<IList<User>> GetUsersByApplicationAsync(
    GraphServiceClient graphClient,
    string applicationId,
    CancellationToken cancellationToken = default)
```

## Password History Validation and Operations

1. **Retrieve Password History**: Get last 12 passwords for user/application
2. **Hash Comparison**: Compare new password against stored BCrypt hashes
3. **Validation Result**: Block if password found in history
4. **History Update**: Add new password to history if validation passes

### Verified Operations Status

#### 1. Custom Registration

- Calls `AuthenticationService` with `operation=register`
- Creates user in Entra External ID with enhanced attributes
- Initializes password history with first password
- Maintains application isolation with `APPLICATION_ID`

#### 2. Custom Forgot Password

- Calls `PasswordService` with `operation=reset-initiate`
- Generates secure reset token
- Sends reset email via SMTP2GO
- Maintains application isolation

#### 3. Unified Password Management

- Calls `PasswordService` with `operation=reset-complete`
- **All operations require current password for enhanced security**
- **Password Reset Mode** (current password required, for logged-in users):
  - Validates current password
  - Validates new password against history
  - Updates password and history
  - Logs out user after successful reset
  - Sends password changed notification email
- **Password Change Mode** (current password required):
  - Validates current password
  - Validates new password against history
  - Updates password and history
  - Keeps user logged in
  - Sends password changed notification email

### Application Isolation Verification

**Storage Structure**:

```
password-history/
├── application-1/
│   ├── user-1.json
│   ├── user-2.json
│   └── ...
├── application-2/
│   ├── user-1.json
│   ├── user-3.json
│   └── ...
└── ...
```

## Email Integration - SendGrid - WIP

1. **Password Reset Email** - Sent during forgot password flow
2. **Password Changed Notification** - Sent after successful password change

### Power Pages Files

```
power-pages-files/
├── registration.html          # ✅ Enhanced with application context
├── registration.js            # ✅ Enhanced - Calls AuthenticationService
├── forgot-password.html       # ✅ Maintains functionality
├── forgot-password.js         # ✅ Calls PasswordService
├── reset-password.html        # ✅ Unified password management (reset & change)
├── reset-password.js          # ✅ Unified - Handles both reset and change operations
└── power-pages-styles.css     # ✅ Consistent styling across all pages
```

**Note**: Login is handled by standard Power Pages Entra ID authentication - no custom login files needed.

### Azure Functions

```
azure-function/
├── AuthenticationService.cs   # ✅ Registration + unused login/credential validation methods
├── PasswordService.cs         # ✅ Enhanced - All password operations + email integration
├── PasswordServiceHelpers.cs  # ✅ Enhanced - v1.0 API and advanced filtering
├── UtilityService.cs          # ✅ System health and configuration validation
├── Services/
│   ├── EmailService.cs        # ✅ Added - Direct SMTP2GO integration
│   ├── IEmailService.cs       # ✅ Email service interface
│   ├── PasswordHistoryService.cs # ✅ Password history management
│   ├── IPasswordHistoryService.cs # ✅ Password history interface
│   ├── RateLimitingService.cs # ✅ Rate limiting implementation
│   └── IConfigurationValidationService.cs # ✅ Configuration validation
├── Models/
│   └── RequestModels.cs       # ✅ Enhanced with application context fields
└── Program.cs                 # ✅ Enhanced dependency injection
```

**Note**: AuthenticationService contains `HandleUserLogin` and `ValidateUserCredentials` methods that are fully implemented but not used by the current frontend, which uses pure Entra External ID redirect instead.

## Monitoring

### Application Insights Integration

- All password operations logged with correlation IDs
- Success/failure rates tracked
- Error details captured

### Health Monitoring - WIP

**UtilityService Health Checks**:

- Azure Function health
- Blob storage connectivity
- SMTP2GO service status
- Graph API connectivity

## Microsoft Documentation References

### Architecture and Design Patterns

- [Azure Architecture Center](https://learn.microsoft.com/en-us/azure/architecture/)
- [Cloud design patterns](https://learn.microsoft.com/en-us/azure/architecture/patterns/)
- [Microservices architecture on Azure](https://learn.microsoft.com/en-us/azure/architecture/microservices/)
- [Azure Well-Architected Framework](https://learn.microsoft.com/en-us/azure/well-architected/)

### Authentication and Identity

- [Microsoft identity platform overview](https://learn.microsoft.com/en-us/entra/identity-platform/v2-overview)
- [Authentication flows and application scenarios](https://learn.microsoft.com/en-us/entra/identity-platform/authentication-flows-app-scenarios)
- [Microsoft Entra External ID](https://learn.microsoft.com/en-us/entra/external-id/external-identities-overview)
- [OAuth 2.0 and OpenID Connect protocols](https://learn.microsoft.com/en-us/entra/identity-platform/v2-protocols)

### Azure Functions Implementation

- [Azure Functions developer guide](https://learn.microsoft.com/en-us/azure/azure-functions/functions-reference)
- [Azure Functions C# developer reference](https://learn.microsoft.com/en-us/azure/azure-functions/functions-dotnet-class-library)
- [Azure Functions HTTP triggers and bindings](https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-http-webhook)
- [Azure Functions best practices](https://learn.microsoft.com/en-us/azure/azure-functions/functions-best-practices)

### Power Pages Development

- [Power Pages developer guide](https://learn.microsoft.com/en-us/power-pages/configure/power-pages-developer-guide)
- [Power Pages web templates](https://learn.microsoft.com/en-us/power-pages/configure/web-templates)
- [Power Pages JavaScript API](https://learn.microsoft.com/en-us/power-pages/configure/write-javascript)
- [Power Pages liquid templates](https://learn.microsoft.com/en-us/power-pages/configure/liquid-overview)

### Microsoft Graph Integration

- [Microsoft Graph SDKs overview](https://learn.microsoft.com/en-us/graph/sdks/sdks-overview)
- [Microsoft Graph .NET SDK](https://learn.microsoft.com/en-us/graph/sdks/sdk-installation#install-the-microsoft-graph-net-sdk)
- [Microsoft Graph error handling](https://learn.microsoft.com/en-us/graph/errors)
- [Microsoft Graph throttling guidance](https://learn.microsoft.com/en-us/graph/throttling)

### Azure Storage and Security

- [Azure Blob Storage developer guide](https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blobs-introduction)
- [Azure Storage security guide](https://learn.microsoft.com/en-us/azure/storage/common/storage-security-guide)
- [Azure Key Vault developer guide](https://learn.microsoft.com/en-us/azure/key-vault/general/developers-guide)
- [Managed identities for Azure resources](https://learn.microsoft.com/en-us/entra/identity/managed-identities-azure-resources/overview)

### Monitoring and Diagnostics

- [Azure Monitor overview](https://learn.microsoft.com/en-us/azure/azure-monitor/overview)
- [Application Insights for Azure Functions](https://learn.microsoft.com/en-us/azure/azure-functions/functions-monitoring)
- [Azure Functions diagnostics](https://learn.microsoft.com/en-us/azure/azure-functions/functions-diagnostics)
- [Log Analytics in Azure Monitor](https://learn.microsoft.com/en-us/azure/azure-monitor/logs/log-analytics-overview)
