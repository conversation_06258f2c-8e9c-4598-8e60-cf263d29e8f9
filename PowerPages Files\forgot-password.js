const SecureConfig = {
  getFunctionUrl(functionName = 'PasswordService') {
    const baseUrl = window.appConfig?.functionUrl;
    if (!baseUrl) {
      return null;
    }
    return `${baseUrl}/api/${functionName}`;
  },

  getFunctionKey() {
    const functionKey = window.appConfig?.passwordFunctionKey;
    if (!functionKey || functionKey.includes('ERROR_MISSING')) {
      return null;
    }
    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey();
    if (!baseUrl || !functionKey) {
      return null;
    }
    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

const PASSWORD_SERVICE_URL = SecureConfig.getFunctionUrl('PasswordService');

const APPLICATION_ID = window.appConfig?.applicationId || "default-application";
const APPLICATION_NAME = window.appConfig?.applicationName ||
                        document.getElementById('applicationName')?.value ||
                        "ApplicationNameNotSet";

if (!PASSWORD_SERVICE_URL) {
  console.error("PasswordService URL not configured");
}

console.log("Forgot Password Configuration:", {
  passwordServiceUrl: PASSWORD_SERVICE_URL,
  applicationId: APPLICATION_ID,
  applicationName: APPLICATION_NAME
});

const InputSanitizer = {
  sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }
};



const errorMessageDiv = $('#errorMessage');
const successMessageDiv = $('#successMessage');
const resetButton = $('#resetButton');
const forgotPasswordForm = $('#forgotPasswordForm');
const emailInput = $('#email');

function showMessage(message, isError = true, timeout = 0) {
  errorMessageDiv.hide();
  successMessageDiv.hide();
  const messageDiv = isError ? errorMessageDiv : successMessageDiv;
  messageDiv.html(message).show();
  if (timeout > 0) {
    setTimeout(() => messageDiv.fadeOut(), timeout);
  }
}

function clearMessages() {
  errorMessageDiv.hide();
  successMessageDiv.hide();
}

function validateEmail() {
  const email = emailInput.val().trim();
  const emailError = $('#emailError');

  if (!email) {
    emailError.text('Email address is required');
    emailInput.addClass('is-invalid');
    return false;
  }

  if (!InputSanitizer.validateEmail(email)) {
    emailError.text('Please enter a valid email address');
    emailInput.addClass('is-invalid');
    return false;
  }

  emailError.text('');
  emailInput.removeClass('is-invalid');
  return true;
}

function validateForm() {
  return validateEmail();
}

async function initiatePasswordReset(email) {
  try {
    const sanitizedEmail = InputSanitizer.sanitizeInput(email);

    if (!InputSanitizer.validateEmail(sanitizedEmail)) {
      throw new Error('Invalid email address');
    }

    const secureUrl = SecureConfig.buildSecureUrl('PasswordService', 'reset-initiate');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    console.log("Sending forgot password request:", {
      email: sanitizedEmail,
      applicationName: APPLICATION_NAME,
      url: secureUrl
    });

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        email: sanitizedEmail,
        applicationName: APPLICATION_NAME
      })
    });

    console.log("Response status:", response.status, response.statusText);

    if (response.status === 429) {
      const responseText = await response.text();
      let result;
      try {
        result = JSON.parse(responseText);
        if (result.retryAfter) {
          const retryAfter = new Date(result.retryAfter);
          const waitTime = Math.ceil((retryAfter - new Date()) / 1000);
          throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
        }
      } catch (parseError) {
        throw new Error('Too many requests. Please wait a moment before trying again.');
      }
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const responseText = await response.text();
    console.log("Response text:", responseText);

    let result;
    if (responseText) {
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON Parse Error:', parseError);
        throw new Error('Invalid response from server. Please try again.');
      }
    } else {
      throw new Error('Empty response from server. Please try again.');
    }

    console.log("Parsed result:", result);

    if (response.ok && result.success) {
      return {
        success: true,
        message: result.message || "If an account with that email exists, you will receive a reset link shortly."
      };
    } else {
      console.log("Reset initiation failed:", result);

      return {
        success: true,
        message: "If an account with that email exists, you will receive a reset link shortly."
      };
    }

  } catch (error) {
    console.error("Password reset initiation error:", error);
    throw error;
  }
}

function initializeFormHandlers() {
  emailInput.blur(function() {
    validateEmail();
  });

  emailInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#emailError').text('');
  });

  forgotPasswordForm.submit(async function(event) {
    event.preventDefault();
    clearMessages();

    try {
      if (!validateForm()) {
        return;
      }

      resetButton.prop("disabled", true);
      resetButton.text("Sending...");

      showMessage("Sending reset link...", false);

      const email = emailInput.val().trim();

      const result = await initiatePasswordReset(email);

      if (result.success) {
        showMessage(result.message, false, true);
        forgotPasswordForm[0].reset();

        emailInput.removeClass('is-invalid');
        $('#emailError').text('');
      }

    } catch (error) {
      console.error("Form submission error:", error);
      showMessage(error.message || "An unexpected error occurred. Please try again.");
    } finally {
      resetButton.prop("disabled", false);
      resetButton.text("Send Reset Link");
    }
  });
}

$(document).ready(function() {
  initializeFormHandlers();
});
