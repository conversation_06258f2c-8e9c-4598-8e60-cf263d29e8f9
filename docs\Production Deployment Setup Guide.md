# Production Deployment Setup Guide

**Date:** August 14th, 2024  
**System:** PowerPagesCustomAuth Azure Functions  
**Purpose:** Step-by-step production deployment with Graph API permissions and scheduling setup

---

## **🔐 Graph API Permissions Setup**

### **Required Permissions for Password Policy Compliance**

| **Permission** | **Type** | **Purpose** | **Required For** |
|----------------|----------|-------------|------------------|
| `AuditLog.Read.All` | Application | Read sign-in logs for absence detection | ✅ Absence handling |
| `User.ReadWrite.All` | Application | Force password change via `ForceChangePasswordNextSignIn` | ✅ Password expiration enforcement |
| `Directory.Read.All` | Application | Read user information for authentication | ✅ User lookup and validation |

### **Step 1: Grant Permissions via Azure Portal**

#### **1.1 Navigate to App Registration**
```
Azure Portal > Entra ID > App registrations > [Your PowerPagesCustomAuth App]
```

#### **1.2 Add API Permissions**
```
1. Click "API permissions"
2. Click "Add a permission"
3. Select "Microsoft Graph"
4. Select "Application permissions"
5. Search and select:
   - AuditLog.Read.All
   - User.ReadWrite.All  
   - Directory.Read.All
6. Click "Add permissions"
7. Click "Grant admin consent for [tenant]"
8. Verify all permissions show "Granted for [tenant]"
```

#### **1.3 Verify Permissions Assignment**
```powershell
# Connect to Microsoft Graph PowerShell
Connect-MgGraph -Scopes "Application.Read.All"

# Get your application
$app = Get-MgApplication -Filter "DisplayName eq 'PowerPagesCustomAuth'"

# Check assigned permissions
Get-MgApplicationAppRoleAssignment -ApplicationId $app.Id
```

### **Step 2: Test Graph API Connectivity**

#### **2.1 Test from Azure Function**
Create a test endpoint in `UtilityFunction.cs`:

```csharp
// Add to UtilityFunction operation switch
"test-graph-api" => await HandleGraphApiTest(req, correlationId, cancellationToken),

private async Task<HttpResponseData> HandleGraphApiTest(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
{
    try
    {
        // Test AuditLog.Read.All permission
        var signIns = await _graphServiceClient.AuditLogs.SignIns
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Top = 1;
            }, cancellationToken);

        // Test User.ReadWrite.All permission  
        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Top = 1;
                requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName" };
            }, cancellationToken);

        return await CreateJsonResponse(req, new
        {
            message = "Graph API connectivity test successful",
            signInLogsAccess = signIns?.Value?.Count >= 0,
            userAccess = users?.Value?.Count >= 0,
            timestamp = DateTime.UtcNow
        }, HttpStatusCode.OK, correlationId);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Graph API connectivity test failed");
        return await CreateErrorResponse(req, $"Graph API test failed: {ex.Message}", correlationId);
    }
}
```

#### **2.2 Test API Call**
```bash
# Test Graph API permissions
curl -X POST "https://your-function-app.azurewebsites.net/api/UtilityService?operation=test-graph-api&code=YOUR_FUNCTION_KEY"
```

---

## **📅 Scheduling Analysis and Recommendation**

### **Current Architecture Analysis**

**UtilityFunction Design:**
- **HTTP Trigger**: `[HttpTrigger(AuthorizationLevel.Function, "post", "options")]`
- **Operation-Based**: Uses query parameter `operation=notify-expiring-passwords`
- **No Built-in Timer**: Requires external scheduling mechanism

### **Scheduling Options Comparison**

| **Option** | **Complexity** | **Cost** | **Reliability** | **Monitoring** | **Recommendation** |
|------------|----------------|----------|-----------------|----------------|-------------------|
| **Azure Logic Apps** | 🟢 Low | 🟡 Medium | 🟢 High | 🟢 Excellent | ✅ **RECOMMENDED** |
| **Timer Trigger Function** | 🟡 Medium | 🟢 Low | 🟢 High | 🟢 Good | ⚠️ Alternative |
| **External Scheduler** | 🔴 High | 🟢 Low | 🟡 Medium | 🔴 Limited | ❌ Not recommended |

### **Recommended Approach: Azure Logic Apps**

#### **Why Azure Logic Apps?**
1. **Native Integration**: Built for Azure Function orchestration
2. **Excellent Monitoring**: Rich logging and error handling
3. **Easy Configuration**: Visual designer for scheduling
4. **Retry Logic**: Built-in retry and error handling
5. **Cost Effective**: Pay-per-execution model

#### **Logic App Configuration**

**Step 1: Create Logic App**
```
Azure Portal > Logic Apps > Create > Consumption (multi-tenant)
Name: PowerPagesCustomAuth-Scheduler
Resource Group: [same as Function App]
Region: [same as Function App]
```

**Step 2: Configure Workflow**
```json
{
  "definition": {
    "$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#",
    "triggers": {
      "Recurrence": {
        "type": "Recurrence",
        "recurrence": {
          "frequency": "Day",
          "interval": 1,
          "timeZone": "UTC",
          "startTime": "2024-08-15T06:00:00Z"
        }
      }
    },
    "actions": {
      "HTTP": {
        "type": "Http",
        "inputs": {
          "method": "POST",
          "uri": "https://your-function-app.azurewebsites.net/api/UtilityService?operation=notify-expiring-passwords&code=@{parameters('functionKey')}",
          "headers": {
            "Content-Type": "application/json"
          }
        },
        "runAfter": {}
      }
    }
  }
}
```

**Step 3: Configure Parameters**
```
Logic App > Parameters > Add parameter
Name: functionKey
Type: SecureString
Value: [Your UtilityService function key]
```

### **Alternative: Timer Trigger Function**

If you prefer a pure Azure Functions approach:

#### **Create Dedicated Timer Function**
```csharp
public class PasswordExpirationScheduler
{
    private readonly ILogger<PasswordExpirationScheduler> _logger;
    private readonly HttpClient _httpClient;
    private readonly string _functionKey;
    private readonly string _functionUrl;

    public PasswordExpirationScheduler(
        ILogger<PasswordExpirationScheduler> logger,
        HttpClient httpClient,
        IConfiguration configuration)
    {
        _logger = logger;
        _httpClient = httpClient;
        _functionKey = configuration["UtilityService:FunctionKey"];
        _functionUrl = configuration["UtilityService:BaseUrl"];
    }

    [Function("PasswordExpirationScheduler")]
    public async Task Run([TimerTrigger("0 0 6 * * *")] TimerInfo myTimer)
    {
        _logger.LogInformation("Password expiration scheduler triggered at: {time}", DateTime.UtcNow);

        try
        {
            var url = $"{_functionUrl}/UtilityService?operation=notify-expiring-passwords&code={_functionKey}";
            var response = await _httpClient.PostAsync(url, null);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Password expiration notifications completed: {response}", content);
            }
            else
            {
                _logger.LogError("Password expiration notification failed: {statusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in password expiration scheduler");
        }
    }
}
```

#### **Timer Trigger Schedule Expressions**
```csharp
// Daily at 6:00 AM UTC
[TimerTrigger("0 0 6 * * *")]

// Every 12 hours
[TimerTrigger("0 0 */12 * * *")]

// Weekdays only at 6:00 AM UTC
[TimerTrigger("0 0 6 * * 1-5")]
```

---

## **🚀 Deployment Checklist**

### **Pre-Deployment Validation**

#### **1. Configuration Variables**
- [ ] `PASSWORD_EXPIRATION_DAYS` = `90`
- [ ] `PASSWORD_WARNING_DAYS` = `15`
- [ ] `PasswordHistory:MaxCount` = `12`
- [ ] All SendGrid template IDs configured
- [ ] All Entra External ID settings configured
- [ ] Azure Key Vault URL configured

#### **2. Azure Key Vault Secrets**
- [ ] `EntraExternalID--ClientSecret` stored
- [ ] `SendGrid--ApiKey` stored
- [ ] Function App has Key Vault access policy
- [ ] Managed Identity enabled on Function App

#### **3. Entra External ID Configuration**
- [ ] Account lockout threshold set to 5 attempts
- [ ] Lockout duration set to 60 seconds (minimum)
- [ ] Sign-up disabled in user flows (`isSignUpAllowed: false`)
- [ ] Graph API permissions granted and consented

### **Deployment Steps**

#### **Step 1: Deploy Azure Function App**
```bash
# Using Azure CLI
az functionapp deployment source config-zip \
  --resource-group your-resource-group \
  --name your-function-app \
  --src deployment.zip
```

#### **Step 2: Configure Application Settings**
```bash
# Set critical configuration
az functionapp config appsettings set \
  --resource-group your-resource-group \
  --name your-function-app \
  --settings \
    "PASSWORD_EXPIRATION_DAYS=90" \
    "PASSWORD_WARNING_DAYS=15" \
    "PasswordHistory:MaxCount=12"
```

#### **Step 3: Test Core Functionality**
```bash
# Test health check
curl "https://your-function-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_FUNCTION_KEY"

# Test Graph API connectivity
curl -X POST "https://your-function-app.azurewebsites.net/api/UtilityService?operation=test-graph-api&code=YOUR_FUNCTION_KEY"

# Test password expiration notifications (dry run)
curl -X POST "https://your-function-app.azurewebsites.net/api/UtilityService?operation=notify-expiring-passwords&code=YOUR_FUNCTION_KEY"
```

#### **Step 4: Configure Scheduling**
- [ ] Create Azure Logic App with daily recurrence
- [ ] Configure HTTP action to call UtilityService
- [ ] Test Logic App execution
- [ ] Monitor first scheduled execution

#### **Step 5: Update Power Pages Configuration**
- [ ] Update `AzureFunctionUrl` to production URL
- [ ] Update all function keys in site settings
- [ ] Test Power Pages authentication flows
- [ ] Verify meta tag configuration extraction

### **Post-Deployment Validation**

#### **1. End-to-End Testing**
- [ ] User registration flow
- [ ] Password reset flow  
- [ ] Password expiration enforcement
- [ ] Absence detection logic
- [ ] Email delivery verification

#### **2. Monitoring Setup**
- [ ] Application Insights configured
- [ ] Log Analytics workspace connected
- [ ] Alert rules for critical failures
- [ ] Dashboard for password policy metrics

#### **3. Security Validation**
- [ ] Function authorization levels correct
- [ ] CORS settings appropriate
- [ ] Key Vault access working
- [ ] Graph API permissions functional

---

## **📊 Monitoring and Maintenance**

### **Key Metrics to Monitor**
- Daily password expiration notification execution
- Graph API call success rates
- Email delivery success rates
- Password policy compliance rates
- User absence detection accuracy

### **Recommended Alerts**
- Password expiration notification failures
- Graph API permission errors
- SendGrid delivery failures
- Function App health check failures

### **Regular Maintenance Tasks**
- Monthly review of password policy compliance
- Quarterly review of Graph API permissions
- Annual review of configuration variables
- Backup of configuration settings before changes

**Status:** ✅ **PRODUCTION READY**  
**Next Steps:** Execute deployment checklist and configure monitoring
