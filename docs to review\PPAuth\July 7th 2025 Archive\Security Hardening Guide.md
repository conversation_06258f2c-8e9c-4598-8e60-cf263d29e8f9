# Security Hardening Guide

Critical security configurations required before deploying the PowerPages Custom Authentication system to production.

## **⚠️ CRITICAL SECURITY FIXES REQUIRED**

### **1. Function Authorization Level** 🚨 **IMMEDIATE ACTION REQUIRED**

**Current State**: All functions are set to `AuthorizationLevel.Anonymous` for testing
**Risk Level**: **CRITICAL** - Functions are publicly accessible without authentication

#### **Fix Required**:
Update all three Azure Functions:

```csharp
// AuthenticationService.cs - Line 53
[HttpTrigger(AuthorizationLevel.Function, "post", "options", Route = null)]

// PasswordService.cs - Line 78  
[HttpTrigger(AuthorizationLevel.Function, "post", "options", Route = null)]

// UtilityService.cs - Line 50
[HttpTrigger(AuthorizationLevel.Function, "get", "post", "options", Route = null)]
```

#### **Implementation Steps**:
1. Update the three files above
2. Redeploy the Function App
3. Get the function keys from Azure Portal
4. Update Power Pages configuration with function keys

### **2. CORS Configuration** 🚨 **HIGH PRIORITY**

**Current State**: CORS allows all origins (`"*"`)
**Risk Level**: **HIGH** - Any website can call your APIs

#### **Fix Required**:
Update `host.json`:

```json
{
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": [
          "https://your-actual-domain.powerappsportals.com",
          "https://your-staging-domain.powerappsportals.com"
        ],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": [
          "Content-Type", 
          "Authorization", 
          "x-functions-key", 
          "X-Requested-With", 
          "X-Client-Version"
        ]
      }
    }
  }
}
```

### **3. Secret Storage** 🚨 **HIGH PRIORITY**

**Current State**: Secrets in plain text configuration
**Risk Level**: **HIGH** - Sensitive data exposed

#### **Required Actions**:

1. **Move to Azure Key Vault**:
   ```json
   {
     "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/ClientSecret/)",
     "SMTP2GOApiKey": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/SMTP2GOApiKey/)"
   }
   ```

2. **Configure Managed Identity**:
   ```bash
   # Enable system-assigned managed identity
   az functionapp identity assign --name your-function-app --resource-group your-rg
   
   # Grant access to Key Vault
   az keyvault set-policy --name your-keyvault --object-id MANAGED_IDENTITY_ID --secret-permissions get
   ```

## **Security Configuration Checklist**

### **Azure Function App Security**

- [ ] **Authorization Level**: Changed from Anonymous to Function
- [ ] **Function Keys**: Generated and securely stored
- [ ] **CORS**: Configured with specific domains only
- [ ] **HTTPS Only**: Enabled in Function App settings
- [ ] **TLS Version**: Set to minimum 1.2
- [ ] **Managed Identity**: Enabled for Key Vault access

### **Key Vault Security**

- [ ] **Access Policies**: Configured for Function App managed identity only
- [ ] **Network Access**: Restricted to Azure services (if applicable)
- [ ] **Soft Delete**: Enabled
- [ ] **Purge Protection**: Enabled for production
- [ ] **Audit Logging**: Enabled

### **Entra External ID Security**

- [ ] **Client Secret**: Stored in Key Vault
- [ ] **Redirect URIs**: Production domains only
- [ ] **API Permissions**: Minimum required permissions only
- [ ] **Admin Consent**: Granted for application permissions
- [ ] **Certificate Authentication**: Consider for enhanced security

### **Storage Account Security**

- [ ] **Access Keys**: Rotated regularly
- [ ] **Shared Access Signatures**: Time-limited and scoped
- [ ] **Network Access**: Restricted if possible
- [ ] **Encryption**: Enabled (default)
- [ ] **Audit Logging**: Enabled

## **Network Security**

### **1. Function App Network Configuration**

```json
{
  "ipSecurityRestrictions": [
    {
      "ipAddress": "0.0.0.0/0",
      "action": "Allow",
      "priority": 100,
      "name": "AllowAll",
      "description": "Allow all traffic (consider restricting in high-security environments)"
    }
  ],
  "scmIpSecurityRestrictions": [
    {
      "ipAddress": "YOUR_ADMIN_IP/32",
      "action": "Allow",
      "priority": 100,
      "name": "AdminAccess"
    }
  ]
}
```

### **2. Private Endpoints (Optional - High Security)**

For maximum security, consider:
- Private endpoints for Function App
- Private endpoints for Storage Account
- Private endpoints for Key Vault
- VNet integration for Function App

## **Application Security**

### **1. Input Validation**

Verify these security measures are in place:

```csharp
// Email validation
if (!InputSanitizer.validateEmail(email)) {
    throw new Error('Invalid email address');
}

// Password validation  
if (!InputSanitizer.validatePassword(password)) {
    throw new Error('Invalid password format');
}

// Name validation
if (!InputSanitizer.validateName(name)) {
    throw new Error('Invalid name format');
}
```

### **2. Rate Limiting**

Current configuration:
```json
{
  "RateLimit:MaxRequestsPerMinute": "60"
}
```

**Recommendations**:
- Monitor actual usage patterns
- Adjust limits based on legitimate traffic
- Consider different limits for different operations

### **3. Password Security**

Current BCrypt configuration:
```json
{
  "PasswordHistory:WorkFactor": "12",
  "PasswordHistory:MaxCount": "12"
}
```

**Security Assessment**: ✅ **SECURE**
- Work factor 12 = 4,096 iterations (appropriate)
- 12 password history (meets compliance requirements)

## **Monitoring and Alerting Security**

### **1. Security Event Monitoring**

Set up alerts for:

```kusto
// Failed authentication attempts
requests
| where url contains "operation=login" and resultCode >= "400"
| summarize FailedAttempts = count() by bin(timestamp, 5m)
| where FailedAttempts > 10

// Rate limiting events
traces
| where message contains "Rate limit exceeded"
| summarize Events = count() by bin(timestamp, 5m)
| where Events > 5

// Unusual activity patterns
requests
| where name contains "AuthenticationService"
| summarize RequestCount = count() by bin(timestamp, 5m)
| where RequestCount > 100
```

### **2. Security Audit Logging**

Ensure these events are logged:
- All authentication attempts (success/failure)
- Password reset requests
- Configuration changes
- Administrative actions

## **Compliance and Governance**

### **1. Data Protection**

- [ ] **Data Encryption**: At rest and in transit
- [ ] **Data Retention**: Policies defined and implemented
- [ ] **Data Backup**: Regular backups configured
- [ ] **Data Recovery**: Recovery procedures tested

### **2. Access Control**

- [ ] **Principle of Least Privilege**: Applied to all accounts
- [ ] **Regular Access Reviews**: Scheduled and documented
- [ ] **Emergency Access**: Procedures defined
- [ ] **Audit Trail**: All access logged and monitored

### **3. Incident Response**

- [ ] **Incident Response Plan**: Documented and tested
- [ ] **Security Contacts**: Defined and current
- [ ] **Escalation Procedures**: Clear and tested
- [ ] **Communication Plan**: Internal and external

## **Security Testing**

### **1. Pre-Production Security Tests**

```bash
# Test function key requirement
curl -X POST "https://your-function-app.azurewebsites.net/api/AuthenticationService?operation=register" \
  -H "Content-Type: application/json" \
  -d '{"test":"data"}'
# Should return 401 Unauthorized

# Test with function key
curl -X POST "https://your-function-app.azurewebsites.net/api/AuthenticationService?operation=register&code=YOUR_FUNCTION_KEY" \
  -H "Content-Type: application/json" \
  -d '{"test":"data"}'
# Should return 400 Bad Request (validation error, not auth error)

# Test CORS from unauthorized domain
# Should be blocked by browser
```

### **2. Penetration Testing Considerations**

Consider professional security testing for:
- Authentication bypass attempts
- Input validation testing
- Rate limiting effectiveness
- Session management security
- Data exposure risks

## **Ongoing Security Maintenance**

### **Daily**
- Monitor security alerts
- Review failed authentication logs
- Check for unusual activity patterns

### **Weekly**
- Review access logs
- Validate backup integrity
- Check for security updates

### **Monthly**
- Rotate access keys
- Review and update security configurations
- Conduct security training

### **Quarterly**
- Security assessment and review
- Update incident response procedures
- Review and test disaster recovery

## **Emergency Security Procedures**

### **Security Incident Response**

1. **Immediate Actions**:
   ```bash
   # Disable Function App if compromised
   az functionapp stop --name your-function-app --resource-group your-rg
   
   # Rotate all secrets immediately
   # Update Key Vault secrets
   # Generate new function keys
   ```

2. **Investigation**:
   - Review Application Insights logs
   - Check for unauthorized access
   - Identify scope of potential breach

3. **Recovery**:
   - Deploy clean version of application
   - Update all credentials
   - Verify system integrity

### **Key Rotation Procedures**

```bash
# Rotate Entra External ID client secret
az ad app credential reset --id YOUR_APP_ID

# Update Key Vault with new secret
az keyvault secret set --vault-name your-keyvault --name ClientSecret --value NEW_SECRET

# Restart Function App to pick up new secret
az functionapp restart --name your-function-app --resource-group your-rg
```

## **Security Validation**

After implementing all security measures:

1. **Run Security Checklist**: Verify all items completed
2. **Test All Functions**: Ensure they work with security enabled
3. **Monitor for Issues**: Watch for authentication failures
4. **Document Configuration**: Record all security settings
5. **Train Team**: Ensure team understands security procedures

**⚠️ CRITICAL**: Do not deploy to production until ALL security measures are implemented and tested.
