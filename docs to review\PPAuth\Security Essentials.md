# Security Essentials

Critical security configurations required before production deployment.

## 🚨 **CRITICAL FIXES REQUIRED**

### **1. Function Authorization** ✅ **CONFIGURED**

**Status**: Functions are properly secured with Function-level authorization

**Current Configuration**:

```csharp
// All functions properly configured:
// AuthenticationService.cs
// PasswordService.cs
// UtilityService.cs

[HttpTrigger(AuthorizationLevel.Function, "post", "options", Route = null)]
//             ^^^^^^^^^^^^^^^^^^^^^^^^ Properly secured
```

**Verification**:

1. Check that function keys are required for API calls
2. Verify Power Pages has correct function key configured
3. Test that unauthorized calls return 401

### **2. CORS Configuration** ⚠️ **IMMEDIATE**

**Current Risk**: Any website can call your APIs

**Fix Required**:

```json
// Update host.json
{
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": ["https://your-actual-domain.powerappsportals.com"],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": [
          "Content-Type",
          "Authorization",
          "x-functions-key",
          "X-Requested-With",
          "X-Client-Version"
        ]
      }
    }
  }
}
```

### **3. Secret Storage** ⚠️ **HIGH PRIORITY**

**Current Risk**: Secrets in plain text configuration

**Fix Required**:

```json
// Move to Azure Key Vault
{
  "EntraExternalID:ClientSecret": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/ClientSecret/)",
  "SMTP2GOApiKey": "@Microsoft.KeyVault(SecretUri=https://vault.vault.azure.net/secrets/SMTP2GOApiKey/)"
}
```

## 🔐 **Security Implementation Steps**

### **Step 1: Enable Function Keys**

**1. Update Code**:

```csharp
// Change all three Azure Functions from:
[HttpTrigger(AuthorizationLevel.Anonymous, ...)]
// To:
[HttpTrigger(AuthorizationLevel.Function, ...)]
```

**2. Redeploy**:

```bash
# Via VS Code: Deploy to Function App
# Or via CLI:
func azure functionapp publish your-function-app-name
```

**3. Get Function Keys**:

- Go to Azure Portal → Your Function App → Functions → Any function → Function Keys
- Copy the default key

**4. Update Power Pages**:

```
Setting Name: AzureFunctionKey
Setting Value: YOUR_FUNCTION_KEY
```

**5. Update JavaScript** (if needed):

```javascript
// In Power Pages JavaScript files, add function key to requests:
const response = await fetch(
  `${AZURE_FUNCTION_URL}?operation=register&code=${FUNCTION_KEY}`,
  {
    method: "POST",
    // ... rest of request
  }
);
```

### **Step 2: Secure CORS**

**1. Update host.json**:

```json
{
  "version": "2.0",
  "extensions": {
    "http": {
      "cors": {
        "allowedOrigins": [
          "https://your-production-domain.powerappsportals.com",
          "https://your-staging-domain.powerappsportals.com"
        ],
        "allowedMethods": ["GET", "POST", "OPTIONS"],
        "allowedHeaders": [
          "Content-Type",
          "Authorization",
          "x-functions-key",
          "X-Requested-With",
          "X-Client-Version"
        ]
      }
    }
  }
}
```

**2. Redeploy** after CORS changes

### **Step 3: Azure Key Vault Setup**

**1. Create Key Vault**:

```bash
az keyvault create \
  --name your-auth-keyvault \
  --resource-group your-rg \
  --location eastus
```

**2. Enable Managed Identity**:

```bash
az functionapp identity assign \
  --name your-function-app \
  --resource-group your-rg
```

**3. Grant Access**:

```bash
# Get the managed identity object ID
IDENTITY_ID=$(az functionapp identity show --name your-function-app --resource-group your-rg --query principalId -o tsv)

# Grant access to Key Vault
az keyvault set-policy \
  --name your-auth-keyvault \
  --object-id $IDENTITY_ID \
  --secret-permissions get
```

**4. Store Secrets**:

```bash
az keyvault secret set \
  --vault-name your-auth-keyvault \
  --name "ClientSecret" \
  --value "YOUR_ACTUAL_CLIENT_SECRET"

az keyvault secret set \
  --vault-name your-auth-keyvault \
  --name "SMTP2GOApiKey" \
  --value "api-YOUR_ACTUAL_SMTP2GO_KEY"
```

**5. Update Function App Settings**:

```bash
az functionapp config appsettings set \
  --name your-function-app \
  --resource-group your-rg \
  --settings \
  "EntraExternalID:ClientSecret=@Microsoft.KeyVault(SecretUri=https://your-auth-keyvault.vault.azure.net/secrets/ClientSecret/)" \
  "SMTP2GOApiKey=@Microsoft.KeyVault(SecretUri=https://your-auth-keyvault.vault.azure.net/secrets/SMTP2GOApiKey/)"
```

## ✅ **Security Validation**

### **Test Function Authorization**

```bash
# This should return 401 Unauthorized:
curl -X POST "https://your-function-app.azurewebsites.net/api/AuthenticationService?operation=register"

# This should work:
curl -X POST "https://your-function-app.azurewebsites.net/api/AuthenticationService?operation=register&code=YOUR_FUNCTION_KEY"
```

### **Test CORS**

- Try accessing from unauthorized domain (should be blocked)
- Verify authorized domain works correctly

### **Test Key Vault**

```bash
# Check configuration loads correctly:
curl "https://your-function-app.azurewebsites.net/api/UtilityService?operation=config-check&code=YOUR_FUNCTION_KEY"
```

## 🛡️ **Additional Security Measures**

### **Network Security**

```json
// Optional: Restrict Function App access by IP
{
  "ipSecurityRestrictions": [
    {
      "ipAddress": "YOUR_OFFICE_IP/32",
      "action": "Allow",
      "priority": 100,
      "name": "OfficeAccess"
    }
  ]
}
```

### **HTTPS Enforcement**

```bash
# Ensure HTTPS only
az functionapp update \
  --name your-function-app \
  --resource-group your-rg \
  --set httpsOnly=true
```

### **TLS Configuration**

```bash
# Set minimum TLS version
az functionapp config set \
  --name your-function-app \
  --resource-group your-rg \
  --min-tls-version 1.2
```

## 📊 **Security Monitoring**

### **Essential Alerts**

```kusto
// Failed authentication attempts
requests
| where url contains "operation=login" and resultCode >= "400"
| summarize count() by bin(timestamp, 5m)
| where count_ > 10

// Rate limiting events
traces
| where message contains "Rate limit exceeded"
| summarize count() by bin(timestamp, 5m)

// Unusual activity
requests
| summarize count() by bin(timestamp, 5m)
| where count_ > 100
```

### **Security Audit Log**

Monitor these events:

- All authentication attempts
- Password reset requests
- Configuration changes
- Function key usage

## 🚨 **Emergency Procedures**

### **Security Incident Response**

```bash
# 1. Immediately disable Function App
az functionapp stop --name your-function-app --resource-group your-rg

# 2. Rotate all secrets
az ad app credential reset --id YOUR_APP_ID
az keyvault secret set --vault-name your-keyvault --name ClientSecret --value NEW_SECRET

# 3. Generate new function keys
# Go to Azure Portal → Function App → App Keys → Regenerate

# 4. Restart with new configuration
az functionapp start --name your-function-app --resource-group your-rg
```

## ✅ **Security Checklist**

Before production deployment:

- [ ] **Authorization Level**: Changed from Anonymous to Function
- [ ] **Function Keys**: Generated and configured in Power Pages
- [ ] **CORS**: Configured with specific domains only
- [ ] **Secrets**: Moved to Azure Key Vault
- [ ] **Managed Identity**: Enabled and configured
- [ ] **HTTPS Only**: Enabled
- [ ] **TLS 1.2**: Minimum version set
- [ ] **Monitoring**: Security alerts configured
- [ ] **Testing**: All security measures tested
- [ ] **Documentation**: Security configuration documented

## 🔄 **Regular Security Maintenance**

### **Monthly**

- Rotate function keys
- Review access logs
- Update security configurations

### **Quarterly**

- Rotate client secrets
- Security assessment
- Update incident response procedures

### **Annually**

- Full security audit
- Penetration testing
- Security training

## ⚠️ **Critical Reminder**

**DO NOT DEPLOY TO PRODUCTION** until all security measures are implemented and tested. The current configuration with Anonymous authorization is for development/testing only and poses significant security risks in production.
