document.addEventListener('DOMContentLoaded', function() {
    console.log('Invitation Error Page loaded');

    initializeErrorPage();

    setupEventListeners();

    loadErrorDetails();
});

function initializeErrorPage() {
    console.log('Initializing invitation error page...');

    if (!window.appConfig) {
        console.error('App configuration not found');
        return;
    }

    console.log('Error page configuration:', {
        applicationName: window.appConfig.applicationName,
        supportEmail: window.appConfig.supportEmail
    });
}

function setupEventListeners() {
    console.log('Event listeners setup complete - contact support only');
}

function loadErrorDetails() {
    try {
        const errorData = sessionStorage.getItem('invitationError');

        if (errorData) {
            const error = JSON.parse(errorData);
            displayErrorDetails(error);

            sessionStorage.removeItem('invitationError');
        } else {
            const urlParams = new URLSearchParams(window.location.search);
            const errorMessage = urlParams.get('error');
            const errorCode = urlParams.get('code');

            if (errorMessage || errorCode) {
                displayErrorDetails({
                    message: errorMessage || 'Unknown error',
                    code: errorCode,
                    source: 'url-parameters',
                    timestamp: new Date().toISOString()
                });
            }
        }
    } catch (error) {
        console.error('Error loading error details:', error);
    }
}

function displayErrorDetails(errorData) {
    console.log('Displaying error details:', errorData);

    if (errorData.message) {
        const errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            const messageText = errorMessage.querySelector('p');
            if (messageText) {
                messageText.textContent = errorData.message;
            }
        }
    }

    updatePageTitle(errorData);
}

function updatePageTitle(errorData) {
    if (!errorData || !errorData.message) return;

    const message = errorData.message.toLowerCase();
    const errorMessage = document.querySelector('.error-message');
    const titleElement = errorMessage?.querySelector('h3');

    if (!titleElement) return;

    if (message.includes('expired')) {
        titleElement.textContent = 'Invitation Expired';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'Your invitation link has expired. Please request a new invitation from your administrator.';
        }
    } else if (message.includes('used') || message.includes('already')) {
        titleElement.textContent = 'Invitation Already Used';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'This invitation has already been used to create an account. If you already have an account, please try logging in.';
        }
    } else if (message.includes('invalid') || message.includes('not found')) {
        titleElement.textContent = 'Invalid Invitation';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'The invitation link appears to be invalid or corrupted. Please check the link and try again, or request a new invitation.';
        }
    }
}

function sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
}

function logUserAction(action, details = {}) {
    try {
        console.log('User action:', action, details);

    } catch (error) {
        console.error('Error logging user action:', error);
    }
}

logUserAction('invitation_error_page_view', {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    referrer: document.referrer
});

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeErrorPage,
        setupEventListeners,
        loadErrorDetails,
        displayErrorDetails,
        updatePageTitle,
        sanitizeInput,
        logUserAction
    };
}
