# Power Pages with Microsoft Entra External ID: MFA and User Flow Configuration

## 1. Enabling MFA for Power Pages with Entra External ID

### Steps Overview

- Set up Microsoft Entra External ID as an identity provider for your Power Pages site
- Register your Power Pages app in your Entra External ID tenant using the Reply URL given by Power Pages
- Configure authentication and user consent in the app registration settings
- Create and edit user flows to control the sign-in experience
- Enable and enforce Multi-Factor Authentication (MFA) via authentication methods and Conditional Access policies
- Test and verify the user experience

## 2. Configuring Entra External ID as an Identity Provider in Power Pages

1. In Power Pages Studio, go to your site's Security > Identity providers
2. Select Microsoft Entra External ID and click Configure
3. Enter a display name and keep this page open—you'll need the Reply URL next

## 3. Registering Your Power Pages Site in the Entra External ID Tenant

1. In the Entra Admin Center, go to App registrations > New registration
2. Enter a name and paste the Reply URL into the Redirect URI field
3. Finish the registration

## 4. Authentication & User Flow Setup

1. In the app registration, under Authentication, enable:
   - Access tokens
   - ID tokens
2. Under API permissions, grant admin consent
3. In External Identities > User flows, create a new flow (e.g., "Email with password" or "Email with one-time code")
4. In your user flow's settings, add your Power Pages application
5. Customize user attributes and sign-in experience as needed

## 5. Understanding Entra User Flows: Password vs. One-Time Code

| User Flow Type           | Password Used? | Email OTP Used?   | Both Required?                 |
| ------------------------ | -------------- | ----------------- | ------------------------------ |
| Email with Password      | Yes            | Only as MFA       | Password primary + code as MFA |
| Email with One-Time Code | No             | Yes (only method) | No                             |

Key points:

- Switching to "email with one-time code" disables password usage
- To use both password and code, use "email with password" as primary with MFA passcode

## 6. Enabling Second Factor (MFA)

### Supported scenarios

- **With "email with password":**
  - Use email OTP or SMS OTP as second factor
- **With "email with one-time code":**
  - Only SMS OTP can act as MFA (can't prompt for second email)

### Implementation steps

1. Ensure your sign-in user flow supports MFA (typically "email with password")
2. In Microsoft Entra Admin Center, configure your user flow
3. Set up a Conditional Access policy:
   - Go to Entra ID > Conditional Access > Policies
   - Create policy with:
     - Users: All users or targeted groups
     - Cloud apps: Your Power Pages app
     - Access controls: Grant access, require MFA
4. Under Authentication methods, enable MFA types (email OTP, SMS, external)
5. Verify users experience MFA challenge during sign-in

> **Note:** MFA via Conditional Access applies after standard sign-in. With "email with one-time code", SMS is the only valid second factor.

## 7. Disabling Security Defaults to Use Conditional Access

If you see "Security defaults must be disabled to enable Conditional Access policy":

1. In Microsoft Entra Admin Center:
   - Go to Entra ID > Overview > Properties
   - Scroll to Manage security defaults
   - Set Security defaults to Disabled
   - Save changes

| Setting            | Description                                 |
| ------------------ | ------------------------------------------- |
| Security Defaults  | Easy baseline, all-or-nothing MFA           |
| Conditional Access | Flexible, granular custom security policies |

> Disabling security defaults is required to manage security with Conditional Access

## 8. Summary of Key Points

- MFA is enforced through Conditional Access policies, not directly via user flows
- Switching to "email with one-time code" flow eliminates password usage
- Only SMS OTP can be a second factor with "email with one-time code" flow
- Disable Security Defaults to use Conditional Access
- Configure MFA requirements and user experience as needed

## 9. MFA Scenarios by User Flow

| User Flow Type           | First Factor | MFA Options                   | Notes                    |
| ------------------------ | ------------ | ----------------------------- | ------------------------ |
| Email with Password      | Password     | Email OTP, SMS OTP, external  | MFA is layered, flexible |
| Email with One-Time Code | Email code   | SMS OTP, external (not email) | Cannot use email twice   |

## 10. Additional Notes

- Always test changes with a non-admin test account
- For maximum flexibility (supporting both login methods), set up multiple user flows or identity providers
