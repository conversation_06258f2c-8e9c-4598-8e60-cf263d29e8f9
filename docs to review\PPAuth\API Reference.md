# API Reference

Complete reference for all PowerPages Custom Authentication endpoints.

## 🔗 **Base URL**

```
https://your-function-app.azurewebsites.net/api
```

## 🔑 **Authentication**

All endpoints require a function key when authorization is set to Function level:

```
?code=YOUR_FUNCTION_KEY
```

## 📋 **AuthenticationService**

### **Validate Credentials**

Validates user credentials without creating a session.

```http
POST /AuthenticationService?operation=validate-credentials
Content-Type: application/json
```

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "applicationName": "Customer Portal"
}
```

**Response (Success)**:

```json
{
  "data": {
    "message": "User registered successfully",
    "userId": "********-1234-1234-1234-************",
    "email": "<EMAIL>"
  },
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

**Response (Error)**:

```json
{
  "success": false,
  "message": "Password has been used recently. Please choose a different password.",
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

### **Validate Credentials** (Optional)

Validates user credentials against password history.

```http
POST /AuthenticationService?operation=validate-credentials
Content-Type: application/json
```

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "applicationName": "Customer Portal"
}
```

### **List Duplicate Users** (Admin)

Lists users with the same email across applications.

```http
POST /AuthenticationService?operation=list-duplicates
Content-Type: application/json
```

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "applicationName": "Customer Portal"
}
```

## 📝 **RegistrationService**

### **Register User**

Creates a new user account with invitation token validation.

```http
POST /RegistrationService?operation=register
Content-Type: application/json
```

**Request Body**:

```json
{
  "token": "inv_abc123...",
  "verificationCode": "XYZ789",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "applicationName": "Customer Portal"
}
```

**Response (Success)**:

```json
{
  "data": {
    "success": true,
    "message": "Account created successfully",
    "userId": "********-1234-1234-1234-************"
  },
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

**Response (Error)**:

```json
{
  "success": false,
  "message": "Invalid or expired invitation token",
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

## 🔐 **PasswordService**

### **Validate Password**

Checks if a password meets requirements and isn't in history.

```http
POST /PasswordService?operation=validate
Content-Type: application/json
```

**Request Body**:

```json
{
  "userId": "<EMAIL>",
  "newPassword": "NewSecurePassword123!",
  "applicationName": "Customer Portal",
  "updateHistory": false
}
```

**Response (Valid)**:

```json
{
  "data": {
    "isValid": true,
    "message": "Password is valid"
  },
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

**Response (Invalid)**:

```json
{
  "success": false,
  "message": "Password has been used recently. Please choose a different password.",
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

### **Initiate Password Reset**

Sends password reset email with verification code.

```http
POST /PasswordService?operation=reset-initiate
Content-Type: application/json
```

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "applicationName": "Customer Portal"
}
```

**Response**:

```json
{
  "data": {
    "message": "Password reset email sent successfully",
    "email": "<EMAIL>"
  },
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

### **Complete Password Reset**

Completes password reset using verification code.

```http
POST /PasswordService?operation=reset-complete
Content-Type: application/json
```

**Request Body**:

```json
{
  "token": "reset-token-from-email",
  "verificationCode": "123456",
  "newPassword": "NewSecurePassword123!",
  "applicationName": "Customer Portal"
}
```

**Response**:

```json
{
  "data": {
    "message": "Password reset completed successfully",
    "email": "<EMAIL>"
  },
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

## 🛠️ **UtilityService**

### **Health Check**

Returns system health status.

```http
GET /UtilityService?operation=health
```

**Response**:

```json
{
  "data": {
    "status": "healthy",
    "timestamp": "2025-07-18T12:00:00Z",
    "version": "3.0.0-simplified",
    "services": {
      "passwordHistoryService": "healthy",
      "rateLimitingService": "healthy",
      "configurationService": "healthy",
      "blobStorage": "healthy"
    },
    "configuration": {
      "maxHistoryCount": 12,
      "workFactor": 12,
      "rateLimitPerMinute": 60,
      "hasSendGridKey": true,
      "functionsRuntime": "dotnet-isolated"
    }
  },
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

### **Configuration Check**

Validates all required configuration settings.

```http
GET /UtilityService?operation=config-check
```

**Response**:

```json
{
  "data": {
    "isValid": true,
    "missingSettings": [],
    "recommendations": [
      "Consider increasing BCrypt work factor to at least 10 for better security"
    ],
    "timestamp": "2025-07-18T12:00:00Z"
  },
  "correlationId": "abc123",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

### **System Statistics**

Returns usage statistics and metrics.

```http
GET /UtilityService?operation=stats
```

**Response**:

```json
{
  "success": true,
  "data": {
    "passwordHistoryEntries": 1250,
    "totalResetTokens": 89,
    "activeResetTokens": 12,
    "expiredTokens": 77,
    "timestamp": "2025-01-07T12:00:00Z"
  }
}
```

### **Token Cleanup**

Removes expired password reset tokens.

```http
GET /UtilityService?operation=cleanup-tokens
```

**Response**:

```json
{
  "success": true,
  "data": {
    "tokensProcessed": 150,
    "tokensRemoved": 45,
    "timestamp": "2025-01-07T12:00:00Z"
  }
}
```

### **Graph API Test**

Tests Microsoft Graph API connectivity.

```http
GET /UtilityService?operation=test-graph
```

**Response**:

```json
{
  "success": true,
  "data": {
    "configurationStatus": {
      "tenantIdConfigured": true,
      "clientIdConfigured": true,
      "clientSecretConfigured": true
    },
    "graphApiTest": "SUCCESS",
    "error": null
  }
}
```

## 🚫 **Error Responses**

### **Common Error Codes**

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing/invalid function key)
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

### **Error Response Format**

```json
{
  "success": false,
  "error": "Error message description",
  "correlationId": "abc123",
  "timestamp": "2025-01-07T12:00:00Z"
}
```

### **Validation Errors**

```json
{
  "success": false,
  "error": "Validation failed",
  "details": {
    "email": ["Email is required"],
    "password": ["Password must be at least 8 characters"]
  },
  "correlationId": "abc123"
}
```

## 🔄 **Rate Limiting**

- **Default Limit**: 60 requests per minute per client
- **Rate Limit Headers**: Included in responses
- **429 Response**: When limit exceeded

```json
{
  "success": false,
  "error": "Too many requests. Please wait 30 seconds before trying again.",
  "correlationId": "abc123"
}
```

## 📝 **Request Headers**

### **Required Headers**

```http
Content-Type: application/json
```

### **Optional Headers**

```http
X-Client-Version: 3.0.0-simplified
X-Requested-With: XMLHttpRequest
```

## 🧪 **Testing Examples**

### **cURL Examples**

**Health Check**:

```bash
curl "https://your-app.azurewebsites.net/api/UtilityService?operation=health&code=YOUR_KEY"
```

**Register User**:

```bash
curl -X POST "https://your-app.azurewebsites.net/api/RegistrationService?operation=register&code=YOUR_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "inv_abc123...",
    "verificationCode": "XYZ789",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "firstName": "Test",
    "lastName": "User",
    "applicationName": "Test App"
  }'
```

**Password Reset**:

```bash
curl -X POST "https://your-app.azurewebsites.net/api/PasswordService?operation=reset-initiate&code=YOUR_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "applicationName": "Test App"
  }'
```

### **JavaScript Examples**

**Register User**:

```javascript
const response = await fetch(
  `${baseUrl}/RegistrationService?operation=register&code=${functionKey}`,
  {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Client-Version": "3.0.0-simplified",
    },
    body: JSON.stringify({
      token: "inv_abc123...",
      verificationCode: "XYZ789",
      email: "<EMAIL>",
      password: "SecurePassword123!",
      firstName: "John",
      lastName: "Doe",
      applicationName: "Customer Portal",
    }),
  }
);

const result = await response.json();
```

## 📊 **Response Times**

**Expected Performance**:

- Health Check: < 500ms
- User Registration: < 2s
- Password Validation: < 1s
- Password Reset: < 3s (includes email)

## 🔍 **Debugging**

### **Correlation IDs**

Every response includes a `correlationId` for tracking requests across logs.

### **Application Insights**

Use correlation IDs to find detailed logs:

```kusto
requests | where customDimensions.CorrelationId == "abc123"
traces | where customDimensions.CorrelationId == "abc123"
```

### **Common Issues**

- **401 Errors**: Check function key
- **CORS Errors**: Verify domain in allowedOrigins
- **500 Errors**: Check Application Insights for details
